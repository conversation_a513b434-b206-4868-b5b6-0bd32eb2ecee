{"logs": [{"outputFile": "com.gokul719.snack97152fc1f368437dac54171df4ba22bd.app-mergeDebugResources-74:/values-ml/values-ml.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\37f986109817451798d14c7952a3b6e1\\transformed\\react-android-0.76.9-debug\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,127,207,282,352,435,504,571,645,720,801,885,954,1034,1116,1196,1278,1364,1442,1515,1587,1683,1756,1836,1904", "endColumns": "71,79,74,69,82,68,66,73,74,80,83,68,79,81,79,81,85,77,72,71,95,72,79,67,72", "endOffsets": "122,202,277,347,430,499,566,640,715,796,880,949,1029,1111,1191,1273,1359,1437,1510,1582,1678,1751,1831,1899,1972"}, "to": {"startLines": "50,64,143,145,146,150,163,164,165,215,216,219,227,230,231,232,234,235,237,239,240,242,245,247,248", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3695,5069,12339,12480,12550,12880,13895,13962,14036,18274,18355,18612,19098,19339,19421,19501,19660,19746,19898,20044,20116,20313,20529,20728,20796", "endColumns": "71,79,74,69,82,68,66,73,74,80,83,68,79,81,79,81,85,77,72,71,95,72,79,67,72", "endOffsets": "3762,5144,12409,12545,12628,12944,13957,14031,14106,18350,18434,18676,19173,19416,19496,19578,19741,19819,19966,20111,20207,20381,20604,20791,20864"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\02b86cfeeba2fc323c383047854b9491\\transformed\\play-services-basement-18.2.0\\res\\values-ml\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "155", "endOffsets": "350"}, "to": {"startLines": "73", "startColumns": "4", "startOffsets": "6207", "endColumns": "159", "endOffsets": "6362"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c53b383fa8484255d5fd97643f23aa97\\transformed\\foundation-release\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,143", "endColumns": "87,91", "endOffsets": "138,230"}, "to": {"startLines": "249,250", "startColumns": "4,4", "startOffsets": "20869,20957", "endColumns": "87,91", "endOffsets": "20952,21044"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f1013ccf4ee59567949af9e71a98b092\\transformed\\exoplayer-ui-2.18.1\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,294,521,725,826,926,1017,1110,1219,1296,1363,1455,1547,1624,1695,1756,1830,1950,2072,2191,2270,2351,2423,2500,2596,2691,2760,2825,2878,2936,2986,3047,3113,3175,3236,3306,3368,3432,3498,3569,3636,3692,3754,3830,3906", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,100,99,90,92,108,76,66,91,91,76,70,60,73,119,121,118,78,80,71,76,95,94,68,64,52,57,49,60,65,61,60,69,61,63,65,70,66,55,61,75,75,53", "endOffsets": "289,516,720,821,921,1012,1105,1214,1291,1358,1450,1542,1619,1690,1751,1825,1945,2067,2186,2265,2346,2418,2495,2591,2686,2755,2820,2873,2931,2981,3042,3108,3170,3231,3301,3363,3427,3493,3564,3631,3687,3749,3825,3901,3955"}, "to": {"startLines": "2,11,15,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,344,571,7977,8078,8178,8269,8362,8471,8548,8615,8707,8799,8876,8947,9008,9082,9202,9324,9443,9522,9603,9675,9752,9848,9943,10012,10787,10840,10898,10948,11009,11075,11137,11198,11268,11330,11394,11460,11531,11598,11654,11716,11792,11868", "endLines": "10,14,18,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138", "endColumns": "17,12,12,100,99,90,92,108,76,66,91,91,76,70,60,73,119,121,118,78,80,71,76,95,94,68,64,52,57,49,60,65,61,60,69,61,63,65,70,66,55,61,75,75,53", "endOffsets": "339,566,770,8073,8173,8264,8357,8466,8543,8610,8702,8794,8871,8942,9003,9077,9197,9319,9438,9517,9598,9670,9747,9843,9938,10007,10072,10835,10893,10943,11004,11070,11132,11193,11263,11325,11389,11455,11526,11593,11649,11711,11787,11863,11917"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\3767e343142fab2c839a998d5bfdc1ca\\transformed\\play-services-ads-22.6.0\\res\\values-ml\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "199,240,291,344,413,484,594,675,781,832,955,1008,1140,1235,1282,1370,1410,1448,1501,1577,1624", "endColumns": "40,50,52,68,70,109,80,105,50,122,52,131,94,46,87,39,37,52,75,46,55", "endOffsets": "239,290,343,412,483,593,674,780,831,954,1007,1139,1234,1281,1369,1409,1447,1500,1576,1623,1679"}, "to": {"startLines": "199,200,201,204,205,206,207,208,209,210,211,212,213,220,221,222,223,224,225,226,251", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "16927,16972,17027,17259,17332,17407,17521,17606,17716,17771,17898,17955,18091,18681,18732,18824,18868,18910,18967,19047,21049", "endColumns": "44,54,56,72,74,113,84,109,54,126,56,135,98,50,91,43,41,56,79,50,59", "endOffsets": "16967,17022,17079,17327,17402,17516,17601,17711,17766,17893,17950,18086,18185,18727,18819,18863,18905,18962,19042,19093,21104"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\51b16512a656ca1125c5acd36144cb6d\\transformed\\exoplayer-core-2.18.1\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,187,259,329,406,487,590,687", "endColumns": "70,60,71,69,76,80,102,96,77", "endOffsets": "121,182,254,324,401,482,585,682,760"}, "to": {"startLines": "112,113,114,115,116,117,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "10077,10148,10209,10281,10351,10428,10509,10612,10709", "endColumns": "70,60,71,69,76,80,102,96,77", "endOffsets": "10143,10204,10276,10346,10423,10504,10607,10704,10782"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\491ff7541ddc657f2c55a259a03f9653\\transformed\\ui-release\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,287,386,490,580,666,767,854,942,1028,1115,1193,1270,1344,1417,1493,1560", "endColumns": "94,86,98,103,89,85,100,86,87,85,86,77,76,73,72,75,66,118", "endOffsets": "195,282,381,485,575,661,762,849,937,1023,1110,1188,1265,1339,1412,1488,1555,1674"}, "to": {"startLines": "62,63,84,85,86,147,148,202,203,217,218,229,233,236,238,243,244,246", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4887,4982,7619,7718,7822,12633,12719,17084,17171,18439,18525,19261,19583,19824,19971,20386,20462,20609", "endColumns": "94,86,98,103,89,85,100,86,87,85,86,77,76,73,72,75,66,118", "endOffsets": "4977,5064,7713,7817,7907,12714,12815,17166,17254,18520,18607,19334,19655,19893,20039,20457,20524,20723"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\263c8e8847d6991526302086ecc6e182\\transformed\\play-services-base-18.0.1\\res\\values-ml\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,308,483,618,735,895,1016,1117,1219,1397,1509,1679,1811,1956,2113,2173,2238", "endColumns": "114,174,134,116,159,120,100,101,177,111,169,131,144,156,59,64,87", "endOffsets": "307,482,617,734,894,1015,1116,1218,1396,1508,1678,1810,1955,2112,2172,2237,2325"}, "to": {"startLines": "65,66,67,68,69,70,71,72,74,75,76,77,78,79,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5149,5268,5447,5586,5707,5871,5996,6101,6367,6549,6665,6839,6975,7124,7285,7349,7418", "endColumns": "118,178,138,120,163,124,104,105,181,115,173,135,148,160,63,68,91", "endOffsets": "5263,5442,5581,5702,5866,5991,6096,6202,6544,6660,6834,6970,7119,7280,7344,7413,7505"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\d139f7b0159c383eb11b4ff91a7586c3\\transformed\\material-1.6.1\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,226,313,407,517,609,674,773,839,899,1001,1077,1135,1213,1278,1332,1449,1513,1577,1631,1711,1845,1931,2020,2126,2211,2299,2394,2461,2527,2606,2688,2779,2855,2932,3009,3080,3187,3267,3364,3464,3538,3619,3724,3782,3849,3940,4032,4094,4158,4221,4324,4440,4545,4661", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,86,93,109,91,64,98,65,59,101,75,57,77,64,53,116,63,63,53,79,133,85,88,105,84,87,94,66,65,78,81,90,75,76,76,70,106,79,96,99,73,80,104,57,66,90,91,61,63,62,102,115,104,115,83", "endOffsets": "221,308,402,512,604,669,768,834,894,996,1072,1130,1208,1273,1327,1444,1508,1572,1626,1706,1840,1926,2015,2121,2206,2294,2389,2456,2522,2601,2683,2774,2850,2927,3004,3075,3182,3262,3359,3459,3533,3614,3719,3777,3844,3935,4027,4089,4153,4216,4319,4435,4540,4656,4740"}, "to": {"startLines": "19,51,59,60,61,87,139,144,149,151,152,153,154,155,156,157,158,159,160,161,162,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,214", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "775,3767,4591,4685,4795,7912,11922,12414,12820,12949,13051,13127,13185,13263,13328,13382,13499,13563,13627,13681,13761,14111,14197,14286,14392,14477,14565,14660,14727,14793,14872,14954,15045,15121,15198,15275,15346,15453,15533,15630,15730,15804,15885,15990,16048,16115,16206,16298,16360,16424,16487,16590,16706,16811,18190", "endLines": "22,51,59,60,61,87,139,144,149,151,152,153,154,155,156,157,158,159,160,161,162,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,214", "endColumns": "12,86,93,109,91,64,98,65,59,101,75,57,77,64,53,116,63,63,53,79,133,85,88,105,84,87,94,66,65,78,81,90,75,76,76,70,106,79,96,99,73,80,104,57,66,90,91,61,63,62,102,115,104,115,83", "endOffsets": "941,3849,4680,4790,4882,7972,12016,12475,12875,13046,13122,13180,13258,13323,13377,13494,13558,13622,13676,13756,13890,14192,14281,14387,14472,14560,14655,14722,14788,14867,14949,15040,15116,15193,15270,15341,15448,15528,15625,15725,15799,15880,15985,16043,16110,16201,16293,16355,16419,16482,16585,16701,16806,16922,18269"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\71743d480ebb399fb4fe6b96265e7bca\\transformed\\core-1.13.1\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,260,362,466,569,670,792", "endColumns": "101,102,101,103,102,100,121,100", "endOffsets": "152,255,357,461,564,665,787,888"}, "to": {"startLines": "52,53,54,55,56,57,58,241", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3854,3956,4059,4161,4265,4368,4469,20212", "endColumns": "101,102,101,103,102,100,121,100", "endOffsets": "3951,4054,4156,4260,4363,4464,4586,20308"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\68cda876f16b6c181c93767ccf9edd36\\transformed\\appcompat-1.7.0\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,318,429,520,625,747,825,900,991,1084,1185,1279,1379,1473,1568,1667,1758,1849,1931,2040,2144,2243,2355,2467,2588,2753,2854", "endColumns": "106,105,110,90,104,121,77,74,90,92,100,93,99,93,94,98,90,90,81,108,103,98,111,111,120,164,100,82", "endOffsets": "207,313,424,515,620,742,820,895,986,1079,1180,1274,1374,1468,1563,1662,1753,1844,1926,2035,2139,2238,2350,2462,2583,2748,2849,2932"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,228", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "946,1053,1159,1270,1361,1466,1588,1666,1741,1832,1925,2026,2120,2220,2314,2409,2508,2599,2690,2772,2881,2985,3084,3196,3308,3429,3594,19178", "endColumns": "106,105,110,90,104,121,77,74,90,92,100,93,99,93,94,98,90,90,81,108,103,98,111,111,120,164,100,82", "endOffsets": "1048,1154,1265,1356,1461,1583,1661,1736,1827,1920,2021,2115,2215,2309,2404,2503,2594,2685,2767,2876,2980,3079,3191,3303,3424,3589,3690,19256"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\268d4161c8b8edb1216e3f661bb7ee02\\transformed\\browser-1.6.0\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,164,267,378", "endColumns": "108,102,110,103", "endOffsets": "159,262,373,477"}, "to": {"startLines": "83,140,141,142", "startColumns": "4,4,4,4", "startOffsets": "7510,12021,12124,12235", "endColumns": "108,102,110,103", "endOffsets": "7614,12119,12230,12334"}}]}]}