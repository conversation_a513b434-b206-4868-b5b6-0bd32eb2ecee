{"logs": [{"outputFile": "com.gokul719.snack97152fc1f368437dac54171df4ba22bd.app-mergeDebugResources-74:/values-ta/values-ta.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\68cda876f16b6c181c93767ccf9edd36\\transformed\\appcompat-1.7.0\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,218,320,435,524,635,756,835,911,1009,1109,1204,1298,1405,1505,1607,1701,1799,1897,1978,2086,2189,2288,2404,2507,2612,2769,2871", "endColumns": "112,101,114,88,110,120,78,75,97,99,94,93,106,99,101,93,97,97,80,107,102,98,115,102,104,156,101,81", "endOffsets": "213,315,430,519,630,751,830,906,1004,1104,1199,1293,1400,1500,1602,1696,1794,1892,1973,2081,2184,2283,2399,2502,2607,2764,2866,2948"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,228", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "922,1035,1137,1252,1341,1452,1573,1652,1728,1826,1926,2021,2115,2222,2322,2424,2518,2616,2714,2795,2903,3006,3105,3221,3324,3429,3586,19078", "endColumns": "112,101,114,88,110,120,78,75,97,99,94,93,106,99,101,93,97,97,80,107,102,98,115,102,104,156,101,81", "endOffsets": "1030,1132,1247,1336,1447,1568,1647,1723,1821,1921,2016,2110,2217,2317,2419,2513,2611,2709,2790,2898,3001,3100,3216,3319,3424,3581,3683,19155"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\268d4161c8b8edb1216e3f661bb7ee02\\transformed\\browser-1.6.0\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,172,274,381", "endColumns": "116,101,106,103", "endOffsets": "167,269,376,480"}, "to": {"startLines": "83,140,141,142", "startColumns": "4,4,4,4", "startOffsets": "7401,11983,12085,12192", "endColumns": "116,101,106,103", "endOffsets": "7513,12080,12187,12291"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\d139f7b0159c383eb11b4ff91a7586c3\\transformed\\material-1.6.1\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,227,311,420,538,622,686,794,862,923,1031,1117,1175,1259,1326,1380,1503,1565,1628,1682,1770,1898,1984,2066,2168,2248,2329,2418,2485,2551,2636,2724,2816,2885,2962,3042,3110,3209,3292,3384,3478,3552,3638,3732,3782,3848,3933,4020,4083,4148,4211,4319,4422,4520,4625", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,83,108,117,83,63,107,67,60,107,85,57,83,66,53,122,61,62,53,87,127,85,81,101,79,80,88,66,65,84,87,91,68,76,79,67,98,82,91,93,73,85,93,49,65,84,86,62,64,62,107,102,97,104,85", "endOffsets": "222,306,415,533,617,681,789,857,918,1026,1112,1170,1254,1321,1375,1498,1560,1623,1677,1765,1893,1979,2061,2163,2243,2324,2413,2480,2546,2631,2719,2811,2880,2957,3037,3105,3204,3287,3379,3473,3547,3633,3727,3777,3843,3928,4015,4078,4143,4206,4314,4417,4515,4620,4706"}, "to": {"startLines": "19,51,59,60,61,87,139,144,149,151,152,153,154,155,156,157,158,159,160,161,162,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,214", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "750,3763,4593,4702,4820,7804,11875,12368,12780,12911,13019,13105,13163,13247,13314,13368,13491,13553,13616,13670,13758,14107,14193,14275,14377,14457,14538,14627,14694,14760,14845,14933,15025,15094,15171,15251,15319,15418,15501,15593,15687,15761,15847,15941,15991,16057,16142,16229,16292,16357,16420,16528,16631,16729,18110", "endLines": "22,51,59,60,61,87,139,144,149,151,152,153,154,155,156,157,158,159,160,161,162,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,214", "endColumns": "12,83,108,117,83,63,107,67,60,107,85,57,83,66,53,122,61,62,53,87,127,85,81,101,79,80,88,66,65,84,87,91,68,76,79,67,98,82,91,93,73,85,93,49,65,84,86,62,64,62,107,102,97,104,85", "endOffsets": "917,3842,4697,4815,4899,7863,11978,12431,12836,13014,13100,13158,13242,13309,13363,13486,13548,13611,13665,13753,13881,14188,14270,14372,14452,14533,14622,14689,14755,14840,14928,15020,15089,15166,15246,15314,15413,15496,15588,15682,15756,15842,15936,15986,16052,16137,16224,16287,16352,16415,16523,16626,16724,16829,18191"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\263c8e8847d6991526302086ecc6e182\\transformed\\play-services-base-18.0.1\\res\\values-ta\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,294,442,565,667,815,939,1048,1145,1321,1424,1573,1704,1854,2006,2064,2123", "endColumns": "100,147,122,101,147,123,108,96,175,102,148,130,149,151,57,58,76", "endOffsets": "293,441,564,666,814,938,1047,1144,1320,1423,1572,1703,1853,2005,2063,2122,2199"}, "to": {"startLines": "65,66,67,68,69,70,71,72,74,75,76,77,78,79,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5165,5270,5422,5549,5655,5807,5935,6048,6310,6490,6597,6750,6885,7039,7195,7257,7320", "endColumns": "104,151,126,105,151,127,112,100,179,106,152,134,153,155,61,62,80", "endOffsets": "5265,5417,5544,5650,5802,5930,6043,6144,6485,6592,6745,6880,7034,7190,7252,7315,7396"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\71743d480ebb399fb4fe6b96265e7bca\\transformed\\core-1.13.1\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,254,353,451,558,673,801", "endColumns": "95,102,98,97,106,114,127,100", "endOffsets": "146,249,348,446,553,668,796,897"}, "to": {"startLines": "52,53,54,55,56,57,58,241", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3847,3943,4046,4145,4243,4350,4465,20135", "endColumns": "95,102,98,97,106,114,127,100", "endOffsets": "3938,4041,4140,4238,4345,4460,4588,20231"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\3767e343142fab2c839a998d5bfdc1ca\\transformed\\play-services-ads-22.6.0\\res\\values-ta\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "199,246,294,350,414,490,595,673,780,831,955,1011,1136,1237,1278,1361,1395,1430,1485,1565,1608", "endColumns": "46,47,55,63,75,104,77,106,50,123,55,124,100,40,82,33,34,54,79,42,55", "endOffsets": "245,293,349,413,489,594,672,779,830,954,1010,1135,1236,1277,1360,1394,1429,1484,1564,1607,1663"}, "to": {"startLines": "199,200,201,204,205,206,207,208,209,210,211,212,213,220,221,222,223,224,225,226,251", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "16834,16885,16937,17183,17251,17331,17440,17522,17633,17688,17816,17876,18005,18597,18642,18729,18767,18806,18865,18949,21001", "endColumns": "50,51,59,67,79,108,81,110,54,127,59,128,104,44,86,37,38,58,83,46,59", "endOffsets": "16880,16932,16992,17246,17326,17435,17517,17628,17683,17811,17871,18000,18105,18637,18724,18762,18801,18860,18944,18991,21056"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f1013ccf4ee59567949af9e71a98b092\\transformed\\exoplayer-ui-2.18.1\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,505,700,789,878,959,1064,1168,1247,1313,1409,1506,1577,1642,1704,1776,1923,2066,2215,2284,2368,2441,2521,2623,2725,2792,2860,2913,2976,3024,3085,3152,3217,3278,3347,3410,3473,3539,3602,3669,3723,3787,3865,3943", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,88,88,80,104,103,78,65,95,96,70,64,61,71,146,142,148,68,83,72,79,101,101,66,67,52,62,47,60,66,64,60,68,62,62,65,62,66,53,63,77,77,55", "endOffsets": "280,500,695,784,873,954,1059,1163,1242,1308,1404,1501,1572,1637,1699,1771,1918,2061,2210,2279,2363,2436,2516,2618,2720,2787,2855,2908,2971,3019,3080,3147,3212,3273,3342,3405,3468,3534,3597,3664,3718,3782,3860,3938,3994"}, "to": {"startLines": "2,11,15,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,335,555,7868,7957,8046,8127,8232,8336,8415,8481,8577,8674,8745,8810,8872,8944,9091,9234,9383,9452,9536,9609,9689,9791,9893,9960,10736,10789,10852,10900,10961,11028,11093,11154,11223,11286,11349,11415,11478,11545,11599,11663,11741,11819", "endLines": "10,14,18,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138", "endColumns": "17,12,12,88,88,80,104,103,78,65,95,96,70,64,61,71,146,142,148,68,83,72,79,101,101,66,67,52,62,47,60,66,64,60,68,62,62,65,62,66,53,63,77,77,55", "endOffsets": "330,550,745,7952,8041,8122,8227,8331,8410,8476,8572,8669,8740,8805,8867,8939,9086,9229,9378,9447,9531,9604,9684,9786,9888,9955,10023,10784,10847,10895,10956,11023,11088,11149,11218,11281,11344,11410,11473,11540,11594,11658,11736,11814,11870"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\51b16512a656ca1125c5acd36144cb6d\\transformed\\exoplayer-core-2.18.1\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,197,266,336,418,499,596,681", "endColumns": "68,72,68,69,81,80,96,84,81", "endOffsets": "119,192,261,331,413,494,591,676,758"}, "to": {"startLines": "112,113,114,115,116,117,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "10028,10097,10170,10239,10309,10391,10472,10569,10654", "endColumns": "68,72,68,69,81,80,96,84,81", "endOffsets": "10092,10165,10234,10304,10386,10467,10564,10649,10731"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\37f986109817451798d14c7952a3b6e1\\transformed\\react-android-0.76.9-debug\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,210,282,350,434,504,571,647,725,807,887,958,1040,1122,1200,1289,1379,1460,1532,1602,1696,1771,1854,1923", "endColumns": "74,79,71,67,83,69,66,75,77,81,79,70,81,81,77,88,89,80,71,69,93,74,82,68,77", "endOffsets": "125,205,277,345,429,499,566,642,720,802,882,953,1035,1117,1195,1284,1374,1455,1527,1597,1691,1766,1849,1918,1996"}, "to": {"startLines": "50,64,143,145,146,150,163,164,165,215,216,219,227,230,231,232,234,235,237,239,240,242,245,247,248", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3688,5085,12296,12436,12504,12841,13886,13953,14029,18196,18278,18526,18996,19250,19332,19410,19584,19674,19828,19971,20041,20236,20460,20663,20732", "endColumns": "74,79,71,67,83,69,66,75,77,81,79,70,81,81,77,88,89,80,71,69,93,74,82,68,77", "endOffsets": "3758,5160,12363,12499,12583,12906,13948,14024,14102,18273,18353,18592,19073,19327,19405,19494,19669,19750,19895,20036,20130,20306,20538,20727,20805"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\491ff7541ddc657f2c55a259a03f9653\\transformed\\ui-release\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,286,380,481,572,655,764,855,950,1032,1118,1208,1293,1366,1437,1517,1586", "endColumns": "96,83,93,100,90,82,108,90,94,81,85,89,84,72,70,79,68,119", "endOffsets": "197,281,375,476,567,650,759,850,945,1027,1113,1203,1288,1361,1432,1512,1581,1701"}, "to": {"startLines": "62,63,84,85,86,147,148,202,203,217,218,229,233,236,238,243,244,246", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4904,5001,7518,7612,7713,12588,12671,16997,17088,18358,18440,19160,19499,19755,19900,20311,20391,20543", "endColumns": "96,83,93,100,90,82,108,90,94,81,85,89,84,72,70,79,68,119", "endOffsets": "4996,5080,7607,7708,7799,12666,12775,17083,17178,18435,18521,19245,19579,19823,19966,20386,20455,20658"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\02b86cfeeba2fc323c383047854b9491\\transformed\\play-services-basement-18.2.0\\res\\values-ta\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "156", "endOffsets": "351"}, "to": {"startLines": "73", "startColumns": "4", "startOffsets": "6149", "endColumns": "160", "endOffsets": "6305"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c53b383fa8484255d5fd97643f23aa97\\transformed\\foundation-release\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,148", "endColumns": "92,97", "endOffsets": "143,241"}, "to": {"startLines": "249,250", "startColumns": "4,4", "startOffsets": "20810,20903", "endColumns": "92,97", "endOffsets": "20898,20996"}}]}]}