1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.gokul719.snack97152fc1f368437dac54171df4ba22bd"
4    android:versionCode="5"
5    android:versionName="1.3.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="34" />
10
11    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
11-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:7:3-75
11-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:7:20-73
12    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
12-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:11:3-76
12-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:11:20-74
13    <uses-permission android:name="android.permission.CAMERA" />
13-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:2:3-62
13-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:2:20-60
14    <uses-permission android:name="android.permission.INTERNET" />
14-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:3:3-64
14-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:3:20-62
15    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
15-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:4:3-77
15-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:4:20-75
16    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
16-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:5:3-77
16-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:5:20-75
17    <uses-permission android:name="android.permission.RECORD_AUDIO" />
17-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:6:3-68
17-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:6:20-66
18    <uses-permission android:name="android.permission.VIBRATE" />
18-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:8:3-63
18-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:8:20-61
19    <uses-permission android:name="android.permission.WAKE_LOCK" />
19-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:9:3-65
19-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:9:20-63
20    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
20-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:10:3-78
20-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:10:20-76
21
22    <queries>
22-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:12:3-18:13
23        <intent>
23-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:13:5-17:14
24            <action android:name="android.intent.action.VIEW" />
24-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:14:7-58
24-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:14:15-56
25
26            <category android:name="android.intent.category.BROWSABLE" />
26-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:15:7-67
26-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:15:17-65
27
28            <data android:scheme="https" />
28-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:16:7-37
28-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:16:13-35
29        </intent>
30
31        <package android:name="host.exp.exponent" /> <!-- Query open documents -->
31-->[:expo-dev-launcher] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-53
31-->[:expo-dev-launcher] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:18-50
32        <intent>
32-->[:expo-file-system] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:9-17:18
33            <action android:name="android.intent.action.OPEN_DOCUMENT_TREE" />
33-->[:expo-file-system] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-79
33-->[:expo-file-system] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:21-76
34        </intent>
35        <intent>
35-->[:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:9-19:18
36
37            <!-- Required for picking images from the camera roll if targeting API 30 -->
38            <action android:name="android.media.action.IMAGE_CAPTURE" />
38-->[:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:13-73
38-->[:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:21-70
39        </intent>
40        <intent>
40-->[:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:9-24:18
41
42            <!-- Required for picking images from the camera if targeting API 30 -->
43            <action android:name="android.media.action.ACTION_VIDEO_CAPTURE" />
43-->[:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:13-80
43-->[:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:21-77
44        </intent>
45        <intent>
45-->[:expo-sharing] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-sharing\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-14:18
46
47            <!-- Required for file sharing if targeting API 30 -->
48            <action android:name="android.intent.action.SEND" />
48-->[:expo-sharing] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-sharing\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-65
48-->[:expo-sharing] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-sharing\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:21-62
49
50            <data android:mimeType="*/*" />
50-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:16:7-37
51        </intent>
52        <intent>
52-->[:expo-speech] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-speech\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:18
53
54            <!-- Required for text-to-speech if targeting API 30 -->
55            <action android:name="android.intent.action.TTS_SERVICE" />
55-->[:expo-speech] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-speech\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-72
55-->[:expo-speech] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-speech\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:21-69
56        </intent>
57        <intent>
57-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\084b6a12791f1cb8a95d16811ba661d8\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:10:9-16:18
58            <action android:name="android.intent.action.GET_CONTENT" />
58-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\084b6a12791f1cb8a95d16811ba661d8\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:11:13-72
58-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\084b6a12791f1cb8a95d16811ba661d8\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:11:21-69
59
60            <category android:name="android.intent.category.OPENABLE" />
60-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\084b6a12791f1cb8a95d16811ba661d8\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:13:13-73
60-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\084b6a12791f1cb8a95d16811ba661d8\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:13:23-70
61
62            <data android:mimeType="*/*" />
62-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:16:7-37
63        </intent> <!-- End of browser content -->
64        <!-- For CustomTabsService -->
65        <intent>
65-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6541660dec70774a79856ee0aa1798c7\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:47:9-49:18
66            <action android:name="android.support.customtabs.action.CustomTabsService" />
66-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6541660dec70774a79856ee0aa1798c7\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:48:13-90
66-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6541660dec70774a79856ee0aa1798c7\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:48:21-87
67        </intent>
68    </queries>
69
70    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
70-->[:react-native-google-mobile-ads] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:5-79
70-->[:react-native-google-mobile-ads] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:22-76
71    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
71-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-81
71-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:22-78
72    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
72-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-77
72-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:22-74
73    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
73-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6541660dec70774a79856ee0aa1798c7\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:27:5-82
73-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6541660dec70774a79856ee0aa1798c7\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:27:22-79
74    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
74-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6541660dec70774a79856ee0aa1798c7\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:28:5-88
74-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6541660dec70774a79856ee0aa1798c7\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:28:22-85
75    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_TOPICS" /> <!-- Required by older versions of Google Play services to create IID tokens -->
75-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6541660dec70774a79856ee0aa1798c7\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:29:5-83
75-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6541660dec70774a79856ee0aa1798c7\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:29:22-80
76    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
76-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c48c306fa29cacf340e1042583924ca8\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:26:5-82
76-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c48c306fa29cacf340e1042583924ca8\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:26:22-79
77    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
77-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:28:5-77
77-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:28:22-74
78
79    <permission
79-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71743d480ebb399fb4fe6b96265e7bca\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
80        android:name="com.gokul719.snack97152fc1f368437dac54171df4ba22bd.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
80-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71743d480ebb399fb4fe6b96265e7bca\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
81        android:protectionLevel="signature" />
81-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71743d480ebb399fb4fe6b96265e7bca\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
82
83    <uses-permission android:name="com.gokul719.snack97152fc1f368437dac54171df4ba22bd.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
83-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71743d480ebb399fb4fe6b96265e7bca\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
83-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71743d480ebb399fb4fe6b96265e7bca\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
84    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" /> <!-- for android -->
84-->[com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\54c08d13b2ebc95f93ef63a72922c9d6\transformed\installreferrer-2.2\AndroidManifest.xml:9:5-110
84-->[com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\54c08d13b2ebc95f93ef63a72922c9d6\transformed\installreferrer-2.2\AndroidManifest.xml:9:22-107
85    <!-- <uses-permission android:name="com.android.launcher.permission.READ_SETTINGS"/> -->
86    <!-- <uses-permission android:name="com.android.launcher.permission.WRITE_SETTINGS"/> -->
87    <!-- <uses-permission android:name="com.android.launcher.permission.INSTALL_SHORTCUT" /> -->
88    <!-- <uses-permission android:name="com.android.launcher.permission.UNINSTALL_SHORTCUT" /> -->
89    <!-- for Samsung -->
90    <uses-permission android:name="com.sec.android.provider.badge.permission.READ" />
90-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f9b119240edcbcd33147b347e305b41e\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:19:5-86
90-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f9b119240edcbcd33147b347e305b41e\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:19:22-83
91    <uses-permission android:name="com.sec.android.provider.badge.permission.WRITE" /> <!-- for htc -->
91-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f9b119240edcbcd33147b347e305b41e\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:20:5-87
91-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f9b119240edcbcd33147b347e305b41e\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:20:22-84
92    <uses-permission android:name="com.htc.launcher.permission.READ_SETTINGS" />
92-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f9b119240edcbcd33147b347e305b41e\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:23:5-81
92-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f9b119240edcbcd33147b347e305b41e\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:23:22-78
93    <uses-permission android:name="com.htc.launcher.permission.UPDATE_SHORTCUT" /> <!-- for sony -->
93-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f9b119240edcbcd33147b347e305b41e\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:24:5-83
93-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f9b119240edcbcd33147b347e305b41e\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:24:22-80
94    <uses-permission android:name="com.sonyericsson.home.permission.BROADCAST_BADGE" />
94-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f9b119240edcbcd33147b347e305b41e\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:27:5-88
94-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f9b119240edcbcd33147b347e305b41e\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:27:22-85
95    <uses-permission android:name="com.sonymobile.home.permission.PROVIDER_INSERT_BADGE" /> <!-- for apex -->
95-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f9b119240edcbcd33147b347e305b41e\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:28:5-92
95-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f9b119240edcbcd33147b347e305b41e\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:28:22-89
96    <uses-permission android:name="com.anddoes.launcher.permission.UPDATE_COUNT" /> <!-- for solid -->
96-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f9b119240edcbcd33147b347e305b41e\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:31:5-84
96-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f9b119240edcbcd33147b347e305b41e\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:31:22-81
97    <uses-permission android:name="com.majeur.launcher.permission.UPDATE_BADGE" /> <!-- for huawei -->
97-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f9b119240edcbcd33147b347e305b41e\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:34:5-83
97-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f9b119240edcbcd33147b347e305b41e\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:34:22-80
98    <uses-permission android:name="com.huawei.android.launcher.permission.CHANGE_BADGE" />
98-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f9b119240edcbcd33147b347e305b41e\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:37:5-91
98-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f9b119240edcbcd33147b347e305b41e\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:37:22-88
99    <uses-permission android:name="com.huawei.android.launcher.permission.READ_SETTINGS" />
99-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f9b119240edcbcd33147b347e305b41e\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:38:5-92
99-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f9b119240edcbcd33147b347e305b41e\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:38:22-89
100    <uses-permission android:name="com.huawei.android.launcher.permission.WRITE_SETTINGS" /> <!-- for ZUK -->
100-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f9b119240edcbcd33147b347e305b41e\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:39:5-93
100-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f9b119240edcbcd33147b347e305b41e\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:39:22-90
101    <uses-permission android:name="android.permission.READ_APP_BADGE" /> <!-- for OPPO -->
101-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f9b119240edcbcd33147b347e305b41e\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:42:5-73
101-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f9b119240edcbcd33147b347e305b41e\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:42:22-70
102    <uses-permission android:name="com.oppo.launcher.permission.READ_SETTINGS" />
102-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f9b119240edcbcd33147b347e305b41e\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:45:5-82
102-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f9b119240edcbcd33147b347e305b41e\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:45:22-79
103    <uses-permission android:name="com.oppo.launcher.permission.WRITE_SETTINGS" /> <!-- for EvMe -->
103-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f9b119240edcbcd33147b347e305b41e\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:46:5-83
103-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f9b119240edcbcd33147b347e305b41e\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:46:22-80
104    <uses-permission android:name="me.everything.badger.permission.BADGE_COUNT_READ" />
104-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f9b119240edcbcd33147b347e305b41e\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:49:5-88
104-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f9b119240edcbcd33147b347e305b41e\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:49:22-85
105    <uses-permission android:name="me.everything.badger.permission.BADGE_COUNT_WRITE" />
105-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f9b119240edcbcd33147b347e305b41e\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:50:5-89
105-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f9b119240edcbcd33147b347e305b41e\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:50:22-86
106
107    <application
107-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:19:3-40:17
108        android:name="com.gokul719.snack97152fc1f368437dac54171df4ba22bd.MainApplication"
108-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:19:16-47
109        android:allowBackup="true"
109-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:19:162-188
110        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
110-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:19:248-316
111        android:debuggable="true"
112        android:extractNativeLibs="false"
113        android:icon="@mipmap/ic_launcher"
113-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:19:81-115
114        android:label="@string/app_name"
114-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:19:48-80
115        android:roundIcon="@mipmap/ic_launcher_round"
115-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:19:116-161
116        android:supportsRtl="true"
116-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:19:221-247
117        android:theme="@style/AppTheme"
117-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:19:189-220
118        android:usesCleartextTraffic="true" >
118-->C:\Users\<USER>\quiz-bee-techs\android\app\src\debug\AndroidManifest.xml:7:18-53
119        <meta-data
119-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:20:5-159
120            android:name="com.google.android.gms.ads.APPLICATION_ID"
120-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:20:16-72
121            android:value="ca-app-pub-9706687137550019~9208363455" />
121-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:20:73-127
122        <meta-data
122-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:21:5-137
123            android:name="com.google.android.gms.ads.DELAY_APP_MEASUREMENT_INIT"
123-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:21:16-84
124            android:value="true" />
124-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:21:85-105
125        <meta-data
125-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:22:5-135
126            android:name="com.google.android.gms.ads.flag.OPTIMIZE_AD_LOADING"
126-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:22:16-82
127            android:value="true" />
127-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:22:83-103
128        <meta-data
128-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:23:5-139
129            android:name="com.google.android.gms.ads.flag.OPTIMIZE_INITIALIZATION"
129-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:23:16-86
130            android:value="true" />
130-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:23:87-107
131        <meta-data
131-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:24:5-83
132            android:name="expo.modules.updates.ENABLED"
132-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:24:16-59
133            android:value="false" />
133-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:24:60-81
134        <meta-data
134-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:25:5-105
135            android:name="expo.modules.updates.EXPO_UPDATES_CHECK_ON_LAUNCH"
135-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:25:16-80
136            android:value="ALWAYS" />
136-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:25:81-103
137        <meta-data
137-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:26:5-99
138            android:name="expo.modules.updates.EXPO_UPDATES_LAUNCH_WAIT_MS"
138-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:26:16-79
139            android:value="0" />
139-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:26:80-97
140
141        <activity
141-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:27:5-39:16
142            android:name="com.gokul719.snack97152fc1f368437dac54171df4ba22bd.MainActivity"
142-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:27:15-43
143            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|screenLayout|uiMode"
143-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:27:44-134
144            android:exported="true"
144-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:27:256-279
145            android:launchMode="singleTask"
145-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:27:135-166
146            android:screenOrientation="portrait"
146-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:27:280-316
147            android:theme="@style/Theme.App.SplashScreen"
147-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:27:210-255
148            android:windowSoftInputMode="adjustResize" >
148-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:27:167-209
149            <intent-filter>
149-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:28:7-31:23
150                <action android:name="android.intent.action.MAIN" />
150-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:29:9-60
150-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:29:17-58
151
152                <category android:name="android.intent.category.LAUNCHER" />
152-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:30:9-68
152-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:30:19-66
153            </intent-filter>
154            <intent-filter>
154-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:32:7-38:23
155                <action android:name="android.intent.action.VIEW" />
155-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:14:7-58
155-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:14:15-56
156
157                <category android:name="android.intent.category.DEFAULT" />
157-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:34:9-67
157-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:34:19-65
158                <category android:name="android.intent.category.BROWSABLE" />
158-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:15:7-67
158-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:15:17-65
159
160                <data android:scheme="com.gokul719.snack97152fc1f368437dac54171df4ba22bd" />
160-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:16:7-37
160-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:16:13-35
161                <data android:scheme="exp+snack-97152fc1-f368-437d-ac54-171df4ba22bd" />
161-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:16:7-37
161-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:16:13-35
162            </intent-filter>
163        </activity>
164        <!--
165           This may generate a warning during your build:
166
167           > property#android.adservices.AD_SERVICES_CONFIG@android:resource
168           > was tagged at AndroidManifest.xml:23 to replace other declarations
169           > but no other declaration present
170
171           You may safely ignore this warning.
172
173           We must include this in case you also use Firebase Analytics in some
174           of its configurations, as it may also include this file, and the two
175           will collide and cause a build error if we don't set this one to take
176           priority via replacement.
177
178           https://github.com/invertase/react-native-google-mobile-ads/issues/657
179        -->
180        <property
180-->[:react-native-google-mobile-ads] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:42:9-45:48
181            android:name="android.adservices.AD_SERVICES_CONFIG"
181-->[:react-native-google-mobile-ads] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:43:13-65
182            android:resource="@xml/gma_ad_services_config" />
182-->[:react-native-google-mobile-ads] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:44:13-59
183
184        <provider
184-->[:react-native-webview] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-16:20
185            android:name="com.reactnativecommunity.webview.RNCWebViewFileProvider"
185-->[:react-native-webview] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-83
186            android:authorities="com.gokul719.snack97152fc1f368437dac54171df4ba22bd.fileprovider"
186-->[:react-native-webview] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-64
187            android:exported="false"
187-->[:react-native-webview] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-37
188            android:grantUriPermissions="true" >
188-->[:react-native-webview] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-47
189            <meta-data
189-->[:react-native-webview] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-15:63
190                android:name="android.support.FILE_PROVIDER_PATHS"
190-->[:react-native-webview] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:17-67
191                android:resource="@xml/file_provider_paths" />
191-->[:react-native-webview] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-60
192        </provider>
193        <provider
193-->[:expo-clipboard] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-clipboard\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-15:20
194            android:name="expo.modules.clipboard.ClipboardFileProvider"
194-->[:expo-clipboard] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-clipboard\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-72
195            android:authorities="com.gokul719.snack97152fc1f368437dac54171df4ba22bd.ClipboardFileProvider"
195-->[:expo-clipboard] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-clipboard\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-73
196            android:exported="true" >
196-->[:expo-clipboard] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-clipboard\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-36
197            <meta-data
197-->[:expo-clipboard] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-clipboard\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-14:68
198                android:name="expo.modules.clipboard.CLIPBOARD_FILE_PROVIDER_PATHS"
198-->[:expo-clipboard] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-clipboard\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:17-84
199                android:resource="@xml/clipboard_provider_paths" />
199-->[:expo-clipboard] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-clipboard\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:17-65
200        </provider>
201
202        <activity
202-->[:expo-dev-launcher] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-25:20
203            android:name="expo.modules.devlauncher.launcher.DevLauncherActivity"
203-->[:expo-dev-launcher] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-81
204            android:exported="true"
204-->[:expo-dev-launcher] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-36
205            android:launchMode="singleTask"
205-->[:expo-dev-launcher] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-44
206            android:theme="@style/Theme.DevLauncher.LauncherActivity" >
206-->[:expo-dev-launcher] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-70
207            <intent-filter>
207-->[:expo-dev-launcher] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-24:29
208                <action android:name="android.intent.action.VIEW" />
208-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:14:7-58
208-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:14:15-56
209
210                <category android:name="android.intent.category.DEFAULT" />
210-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:34:9-67
210-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:34:19-65
211                <category android:name="android.intent.category.BROWSABLE" />
211-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:15:7-67
211-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:15:17-65
212
213                <data android:scheme="expo-dev-launcher" />
213-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:16:7-37
213-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:16:13-35
214            </intent-filter>
215        </activity>
216        <activity
216-->[:expo-dev-launcher] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:9-29:70
217            android:name="expo.modules.devlauncher.launcher.errors.DevLauncherErrorActivity"
217-->[:expo-dev-launcher] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-93
218            android:screenOrientation="portrait"
218-->[:expo-dev-launcher] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-49
219            android:theme="@style/Theme.DevLauncher.ErrorActivity" />
219-->[:expo-dev-launcher] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:13-67
220        <activity
220-->[:expo-dev-menu] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-21:20
221            android:name="expo.modules.devmenu.DevMenuActivity"
221-->[:expo-dev-menu] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-64
222            android:exported="true"
222-->[:expo-dev-menu] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-36
223            android:launchMode="singleTask"
223-->[:expo-dev-menu] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-44
224            android:theme="@style/Theme.AppCompat.Transparent.NoActionBar" >
224-->[:expo-dev-menu] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-75
225            <intent-filter>
225-->[:expo-dev-menu] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-20:29
226                <action android:name="android.intent.action.VIEW" />
226-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:14:7-58
226-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:14:15-56
227
228                <category android:name="android.intent.category.DEFAULT" />
228-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:34:9-67
228-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:34:19-65
229                <category android:name="android.intent.category.BROWSABLE" />
229-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:15:7-67
229-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:15:17-65
230
231                <data android:scheme="expo-dev-menu" />
231-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:16:7-37
231-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:16:13-35
232            </intent-filter>
233        </activity>
234
235        <provider
235-->[:expo-file-system] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:9-30:20
236            android:name="expo.modules.filesystem.FileSystemFileProvider"
236-->[:expo-file-system] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-74
237            android:authorities="com.gokul719.snack97152fc1f368437dac54171df4ba22bd.FileSystemFileProvider"
237-->[:expo-file-system] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:13-74
238            android:exported="false"
238-->[:expo-file-system] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-37
239            android:grantUriPermissions="true" >
239-->[:expo-file-system] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:13-47
240            <meta-data
240-->[:react-native-webview] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-15:63
241                android:name="android.support.FILE_PROVIDER_PATHS"
241-->[:react-native-webview] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:17-67
242                android:resource="@xml/file_system_provider_paths" />
242-->[:react-native-webview] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-60
243        </provider>
244
245        <service
245-->[:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:9-40:19
246            android:name="com.google.android.gms.metadata.ModuleDependencies"
246-->[:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:13-78
247            android:enabled="false"
247-->[:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:13-36
248            android:exported="false" >
248-->[:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:13-37
249            <intent-filter>
249-->[:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:33:13-35:29
250                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
250-->[:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:34:17-94
250-->[:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:34:25-91
251            </intent-filter>
252
253            <meta-data
253-->[:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:37:13-39:36
254                android:name="photopicker_activity:0:required"
254-->[:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:38:17-63
255                android:value="" />
255-->[:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:39:17-33
256        </service>
257
258        <activity
258-->[:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:42:9-44:59
259            android:name="com.canhub.cropper.CropImageActivity"
259-->[:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:43:13-64
260            android:exported="true"
260-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\084b6a12791f1cb8a95d16811ba661d8\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:35:13-36
261            android:theme="@style/Base.Theme.AppCompat" /> <!-- https://developer.android.com/guide/topics/manifest/provider-element.html -->
261-->[:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:44:13-56
262        <provider
262-->[:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:46:9-54:20
263            android:name="expo.modules.imagepicker.fileprovider.ImagePickerFileProvider"
263-->[:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:47:13-89
264            android:authorities="com.gokul719.snack97152fc1f368437dac54171df4ba22bd.ImagePickerFileProvider"
264-->[:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:48:13-75
265            android:exported="false"
265-->[:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:49:13-37
266            android:grantUriPermissions="true" >
266-->[:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:50:13-47
267            <meta-data
267-->[:react-native-webview] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-15:63
268                android:name="android.support.FILE_PROVIDER_PATHS"
268-->[:react-native-webview] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:17-67
269                android:resource="@xml/image_picker_provider_paths" />
269-->[:react-native-webview] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-60
270        </provider>
271
272        <service
272-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:9-17:19
273            android:name="expo.modules.notifications.service.ExpoFirebaseMessagingService"
273-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-91
274            android:exported="false" >
274-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-37
275            <intent-filter android:priority="-1" >
275-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-16:29
275-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:28-49
276                <action android:name="com.google.firebase.MESSAGING_EVENT" />
276-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-78
276-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:25-75
277            </intent-filter>
278        </service>
279
280        <receiver
280-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:9-31:20
281            android:name="expo.modules.notifications.service.NotificationsService"
281-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-83
282            android:enabled="true"
282-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-35
283            android:exported="false" >
283-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-37
284            <intent-filter android:priority="-1" >
284-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:13-30:29
284-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:28-49
285                <action android:name="expo.modules.notifications.NOTIFICATION_EVENT" />
285-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:17-88
285-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:25-85
286                <action android:name="android.intent.action.BOOT_COMPLETED" />
286-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:17-79
286-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:25-76
287                <action android:name="android.intent.action.REBOOT" />
287-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:17-71
287-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:25-68
288                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
288-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:17-82
288-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:25-79
289                <action android:name="com.htc.intent.action.QUICKBOOT_POWERON" />
289-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:17-82
289-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:25-79
290                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
290-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:17-84
290-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:25-81
291            </intent-filter>
292        </receiver>
293
294        <activity
294-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:33:9-40:75
295            android:name="expo.modules.notifications.service.NotificationForwarderActivity"
295-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:34:13-92
296            android:excludeFromRecents="true"
296-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:35:13-46
297            android:exported="false"
297-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:36:13-37
298            android:launchMode="standard"
298-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:37:13-42
299            android:noHistory="true"
299-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:38:13-37
300            android:taskAffinity=""
300-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:39:13-36
301            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
301-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:40:13-72
302
303        <provider
303-->[:expo-sharing] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-sharing\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:9-26:20
304            android:name="expo.modules.sharing.SharingFileProvider"
304-->[:expo-sharing] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-sharing\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:13-68
305            android:authorities="com.gokul719.snack97152fc1f368437dac54171df4ba22bd.SharingFileProvider"
305-->[:expo-sharing] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-sharing\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-71
306            android:exported="false"
306-->[:expo-sharing] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-sharing\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-37
307            android:grantUriPermissions="true" >
307-->[:expo-sharing] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-sharing\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-47
308            <meta-data
308-->[:react-native-webview] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-15:63
309                android:name="android.support.FILE_PROVIDER_PATHS"
309-->[:react-native-webview] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:17-67
310                android:resource="@xml/sharing_provider_paths" />
310-->[:react-native-webview] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-60
311        </provider>
312
313        <meta-data
313-->[:expo-modules-core] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-11:89
314            android:name="org.unimodules.core.AppLoader#react-native-headless"
314-->[:expo-modules-core] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-79
315            android:value="expo.modules.adapters.react.apploader.RNHeadlessAppLoader" />
315-->[:expo-modules-core] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-86
316        <meta-data
316-->[:expo-modules-core] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-15:45
317            android:name="com.facebook.soloader.enabled"
317-->[:expo-modules-core] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-57
318            android:value="true" />
318-->[:expo-modules-core] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-33
319
320        <activity
320-->[com.facebook.react:react-android:0.76.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\37f986109817451798d14c7952a3b6e1\transformed\react-android-0.76.9-debug\AndroidManifest.xml:19:9-21:40
321            android:name="com.facebook.react.devsupport.DevSettingsActivity"
321-->[com.facebook.react:react-android:0.76.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\37f986109817451798d14c7952a3b6e1\transformed\react-android-0.76.9-debug\AndroidManifest.xml:20:13-77
322            android:exported="false" />
322-->[com.facebook.react:react-android:0.76.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\37f986109817451798d14c7952a3b6e1\transformed\react-android-0.76.9-debug\AndroidManifest.xml:21:13-37
323
324        <meta-data
324-->[com.github.bumptech.glide:okhttp3-integration:4.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\89b40201870c87f9d6b8ecc165e999d7\transformed\okhttp3-integration-4.12.0\AndroidManifest.xml:11:9-13:43
325            android:name="com.bumptech.glide.integration.okhttp3.OkHttpGlideModule"
325-->[com.github.bumptech.glide:okhttp3-integration:4.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\89b40201870c87f9d6b8ecc165e999d7\transformed\okhttp3-integration-4.12.0\AndroidManifest.xml:12:13-84
326            android:value="GlideModule" />
326-->[com.github.bumptech.glide:okhttp3-integration:4.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\89b40201870c87f9d6b8ecc165e999d7\transformed\okhttp3-integration-4.12.0\AndroidManifest.xml:13:13-40
327
328        <provider
328-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\084b6a12791f1cb8a95d16811ba661d8\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:23:9-31:20
329            android:name="com.canhub.cropper.CropFileProvider"
329-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\084b6a12791f1cb8a95d16811ba661d8\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:24:13-63
330            android:authorities="com.gokul719.snack97152fc1f368437dac54171df4ba22bd.cropper.fileprovider"
330-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\084b6a12791f1cb8a95d16811ba661d8\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:25:13-72
331            android:exported="false"
331-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\084b6a12791f1cb8a95d16811ba661d8\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:26:13-37
332            android:grantUriPermissions="true" >
332-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\084b6a12791f1cb8a95d16811ba661d8\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:27:13-47
333            <meta-data
333-->[:react-native-webview] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-15:63
334                android:name="android.support.FILE_PROVIDER_PATHS"
334-->[:react-native-webview] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:17-67
335                android:resource="@xml/library_file_paths" />
335-->[:react-native-webview] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-60
336        </provider> <!-- Include the AdActivity and InAppPurchaseActivity configChanges and themes. -->
337        <activity
337-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6541660dec70774a79856ee0aa1798c7\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:56:9-61:43
338            android:name="com.google.android.gms.ads.AdActivity"
338-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6541660dec70774a79856ee0aa1798c7\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:57:13-65
339            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
339-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6541660dec70774a79856ee0aa1798c7\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:58:13-122
340            android:exported="false"
340-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6541660dec70774a79856ee0aa1798c7\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:59:13-37
341            android:theme="@android:style/Theme.Translucent" />
341-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6541660dec70774a79856ee0aa1798c7\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:60:13-61
342
343        <provider
343-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6541660dec70774a79856ee0aa1798c7\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:63:9-68:43
344            android:name="com.google.android.gms.ads.MobileAdsInitProvider"
344-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6541660dec70774a79856ee0aa1798c7\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:64:13-76
345            android:authorities="com.gokul719.snack97152fc1f368437dac54171df4ba22bd.mobileadsinitprovider"
345-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6541660dec70774a79856ee0aa1798c7\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:65:13-73
346            android:exported="false"
346-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6541660dec70774a79856ee0aa1798c7\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:66:13-37
347            android:initOrder="100" />
347-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6541660dec70774a79856ee0aa1798c7\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:67:13-36
348
349        <service
349-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6541660dec70774a79856ee0aa1798c7\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:70:9-74:43
350            android:name="com.google.android.gms.ads.AdService"
350-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6541660dec70774a79856ee0aa1798c7\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:71:13-64
351            android:enabled="true"
351-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6541660dec70774a79856ee0aa1798c7\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:72:13-35
352            android:exported="false" />
352-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6541660dec70774a79856ee0aa1798c7\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:73:13-37
353
354        <activity
354-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6541660dec70774a79856ee0aa1798c7\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:76:9-80:43
355            android:name="com.google.android.gms.ads.OutOfContextTestingActivity"
355-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6541660dec70774a79856ee0aa1798c7\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:77:13-82
356            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
356-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6541660dec70774a79856ee0aa1798c7\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:78:13-122
357            android:exported="false" />
357-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6541660dec70774a79856ee0aa1798c7\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:79:13-37
358        <activity
358-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6541660dec70774a79856ee0aa1798c7\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:81:9-88:43
359            android:name="com.google.android.gms.ads.NotificationHandlerActivity"
359-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6541660dec70774a79856ee0aa1798c7\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:82:13-82
360            android:excludeFromRecents="true"
360-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6541660dec70774a79856ee0aa1798c7\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:83:13-46
361            android:exported="false"
361-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6541660dec70774a79856ee0aa1798c7\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:84:13-37
362            android:launchMode="singleTask"
362-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6541660dec70774a79856ee0aa1798c7\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:85:13-44
363            android:taskAffinity=""
363-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6541660dec70774a79856ee0aa1798c7\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:86:13-36
364            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
364-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6541660dec70774a79856ee0aa1798c7\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:87:13-72
365
366        <receiver
366-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c48c306fa29cacf340e1042583924ca8\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:29:9-40:20
367            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
367-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c48c306fa29cacf340e1042583924ca8\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:30:13-78
368            android:exported="true"
368-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c48c306fa29cacf340e1042583924ca8\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:31:13-36
369            android:permission="com.google.android.c2dm.permission.SEND" >
369-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c48c306fa29cacf340e1042583924ca8\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:32:13-73
370            <intent-filter>
370-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c48c306fa29cacf340e1042583924ca8\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:33:13-35:29
371                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
371-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c48c306fa29cacf340e1042583924ca8\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:34:17-81
371-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c48c306fa29cacf340e1042583924ca8\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:34:25-78
372            </intent-filter>
373
374            <meta-data
374-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c48c306fa29cacf340e1042583924ca8\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:37:13-39:40
375                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
375-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c48c306fa29cacf340e1042583924ca8\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:38:17-92
376                android:value="true" />
376-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c48c306fa29cacf340e1042583924ca8\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:39:17-37
377        </receiver>
378        <!--
379             FirebaseMessagingService performs security checks at runtime,
380             but set to not exported to explicitly avoid allowing another app to call it.
381        -->
382        <service
382-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c48c306fa29cacf340e1042583924ca8\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:46:9-53:19
383            android:name="com.google.firebase.messaging.FirebaseMessagingService"
383-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c48c306fa29cacf340e1042583924ca8\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:47:13-82
384            android:directBootAware="true"
384-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c48c306fa29cacf340e1042583924ca8\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:48:13-43
385            android:exported="false" >
385-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c48c306fa29cacf340e1042583924ca8\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:49:13-37
386            <intent-filter android:priority="-500" >
386-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-16:29
386-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:28-49
387                <action android:name="com.google.firebase.MESSAGING_EVENT" />
387-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-78
387-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:25-75
388            </intent-filter>
389        </service>
390        <service
390-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c48c306fa29cacf340e1042583924ca8\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:54:9-63:19
391            android:name="com.google.firebase.components.ComponentDiscoveryService"
391-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c48c306fa29cacf340e1042583924ca8\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:55:13-84
392            android:directBootAware="true"
392-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3af9264cb24c523080e789fe1aeda055\transformed\firebase-common-21.0.0\AndroidManifest.xml:32:13-43
393            android:exported="false" >
393-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c48c306fa29cacf340e1042583924ca8\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:56:13-37
394            <meta-data
394-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c48c306fa29cacf340e1042583924ca8\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:57:13-59:85
395                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
395-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c48c306fa29cacf340e1042583924ca8\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:58:17-122
396                android:value="com.google.firebase.components.ComponentRegistrar" />
396-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c48c306fa29cacf340e1042583924ca8\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:59:17-82
397            <meta-data
397-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c48c306fa29cacf340e1042583924ca8\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:60:13-62:85
398                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
398-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c48c306fa29cacf340e1042583924ca8\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:61:17-119
399                android:value="com.google.firebase.components.ComponentRegistrar" />
399-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c48c306fa29cacf340e1042583924ca8\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:62:17-82
400            <meta-data
400-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3abe18b93b03aa88f4479351be1225c9\transformed\firebase-installations-17.2.0\AndroidManifest.xml:15:13-17:85
401                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
401-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3abe18b93b03aa88f4479351be1225c9\transformed\firebase-installations-17.2.0\AndroidManifest.xml:16:17-130
402                android:value="com.google.firebase.components.ComponentRegistrar" />
402-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3abe18b93b03aa88f4479351be1225c9\transformed\firebase-installations-17.2.0\AndroidManifest.xml:17:17-82
403            <meta-data
403-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3abe18b93b03aa88f4479351be1225c9\transformed\firebase-installations-17.2.0\AndroidManifest.xml:18:13-20:85
404                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
404-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3abe18b93b03aa88f4479351be1225c9\transformed\firebase-installations-17.2.0\AndroidManifest.xml:19:17-127
405                android:value="com.google.firebase.components.ComponentRegistrar" />
405-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3abe18b93b03aa88f4479351be1225c9\transformed\firebase-installations-17.2.0\AndroidManifest.xml:20:17-82
406            <meta-data
406-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c58bf33df59e38220a67da64c5f2651f\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
407                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
407-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c58bf33df59e38220a67da64c5f2651f\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
408                android:value="com.google.firebase.components.ComponentRegistrar" />
408-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c58bf33df59e38220a67da64c5f2651f\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
409            <meta-data
409-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3af9264cb24c523080e789fe1aeda055\transformed\firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
410                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
410-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3af9264cb24c523080e789fe1aeda055\transformed\firebase-common-21.0.0\AndroidManifest.xml:36:17-109
411                android:value="com.google.firebase.components.ComponentRegistrar" />
411-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3af9264cb24c523080e789fe1aeda055\transformed\firebase-common-21.0.0\AndroidManifest.xml:37:17-82
412            <meta-data
412-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c425ae13f88cc4eff0fa0c7b762ff882\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:25:13-27:85
413                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
413-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c425ae13f88cc4eff0fa0c7b762ff882\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:26:17-115
414                android:value="com.google.firebase.components.ComponentRegistrar" />
414-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c425ae13f88cc4eff0fa0c7b762ff882\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:27:17-82
415        </service>
416
417        <provider
417-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3af9264cb24c523080e789fe1aeda055\transformed\firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
418            android:name="com.google.firebase.provider.FirebaseInitProvider"
418-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3af9264cb24c523080e789fe1aeda055\transformed\firebase-common-21.0.0\AndroidManifest.xml:24:13-77
419            android:authorities="com.gokul719.snack97152fc1f368437dac54171df4ba22bd.firebaseinitprovider"
419-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3af9264cb24c523080e789fe1aeda055\transformed\firebase-common-21.0.0\AndroidManifest.xml:25:13-72
420            android:directBootAware="true"
420-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3af9264cb24c523080e789fe1aeda055\transformed\firebase-common-21.0.0\AndroidManifest.xml:26:13-43
421            android:exported="false"
421-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3af9264cb24c523080e789fe1aeda055\transformed\firebase-common-21.0.0\AndroidManifest.xml:27:13-37
422            android:initOrder="100" />
422-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3af9264cb24c523080e789fe1aeda055\transformed\firebase-common-21.0.0\AndroidManifest.xml:28:13-36
423
424        <activity
424-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\263c8e8847d6991526302086ecc6e182\transformed\play-services-base-18.0.1\AndroidManifest.xml:20:9-22:45
425            android:name="com.google.android.gms.common.api.GoogleApiActivity"
425-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\263c8e8847d6991526302086ecc6e182\transformed\play-services-base-18.0.1\AndroidManifest.xml:20:19-85
426            android:exported="false"
426-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\263c8e8847d6991526302086ecc6e182\transformed\play-services-base-18.0.1\AndroidManifest.xml:22:19-43
427            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
427-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\263c8e8847d6991526302086ecc6e182\transformed\play-services-base-18.0.1\AndroidManifest.xml:21:19-78
428
429        <provider
429-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:31:9-39:20
430            android:name="androidx.startup.InitializationProvider"
430-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:32:13-67
431            android:authorities="com.gokul719.snack97152fc1f368437dac54171df4ba22bd.androidx-startup"
431-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:33:13-68
432            android:exported="false" >
432-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:34:13-37
433            <meta-data
433-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:36:13-38:52
434                android:name="androidx.work.WorkManagerInitializer"
434-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:37:17-68
435                android:value="androidx.startup" />
435-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:38:17-49
436            <meta-data
436-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\270359720fa256563d55dec8f9480bc0\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
437                android:name="androidx.emoji2.text.EmojiCompatInitializer"
437-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\270359720fa256563d55dec8f9480bc0\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
438                android:value="androidx.startup" />
438-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\270359720fa256563d55dec8f9480bc0\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
439            <meta-data
439-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b3059939b2c8092c03a8dedf19cca0e7\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:29:13-31:52
440                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
440-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b3059939b2c8092c03a8dedf19cca0e7\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:30:17-78
441                android:value="androidx.startup" />
441-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b3059939b2c8092c03a8dedf19cca0e7\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:31:17-49
442            <meta-data
442-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b4fc0c5c3b80165703f803a1e31a276f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
443                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
443-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b4fc0c5c3b80165703f803a1e31a276f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
444                android:value="androidx.startup" />
444-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b4fc0c5c3b80165703f803a1e31a276f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
445        </provider>
446
447        <service
447-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:41:9-46:35
448            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
448-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:42:13-88
449            android:directBootAware="false"
449-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:43:13-44
450            android:enabled="@bool/enable_system_alarm_service_default"
450-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:44:13-72
451            android:exported="false" />
451-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:45:13-37
452        <service
452-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:47:9-53:35
453            android:name="androidx.work.impl.background.systemjob.SystemJobService"
453-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:48:13-84
454            android:directBootAware="false"
454-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:49:13-44
455            android:enabled="@bool/enable_system_job_service_default"
455-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:50:13-70
456            android:exported="true"
456-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:51:13-36
457            android:permission="android.permission.BIND_JOB_SERVICE" />
457-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:52:13-69
458        <service
458-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:54:9-59:35
459            android:name="androidx.work.impl.foreground.SystemForegroundService"
459-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:55:13-81
460            android:directBootAware="false"
460-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:56:13-44
461            android:enabled="@bool/enable_system_foreground_service_default"
461-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:57:13-77
462            android:exported="false" />
462-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:58:13-37
463
464        <receiver
464-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:61:9-66:35
465            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
465-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:62:13-88
466            android:directBootAware="false"
466-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:63:13-44
467            android:enabled="true"
467-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:64:13-35
468            android:exported="false" />
468-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:65:13-37
469        <receiver
469-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:67:9-77:20
470            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
470-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:68:13-106
471            android:directBootAware="false"
471-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:69:13-44
472            android:enabled="false"
472-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:70:13-36
473            android:exported="false" >
473-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:71:13-37
474            <intent-filter>
474-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:73:13-76:29
475                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
475-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:74:17-87
475-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:74:25-84
476                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
476-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:75:17-90
476-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:75:25-87
477            </intent-filter>
478        </receiver>
479        <receiver
479-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:78:9-88:20
480            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
480-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:79:13-104
481            android:directBootAware="false"
481-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:80:13-44
482            android:enabled="false"
482-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:81:13-36
483            android:exported="false" >
483-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:82:13-37
484            <intent-filter>
484-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:84:13-87:29
485                <action android:name="android.intent.action.BATTERY_OKAY" />
485-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:85:17-77
485-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:85:25-74
486                <action android:name="android.intent.action.BATTERY_LOW" />
486-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:86:17-76
486-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:86:25-73
487            </intent-filter>
488        </receiver>
489        <receiver
489-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:89:9-99:20
490            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
490-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:90:13-104
491            android:directBootAware="false"
491-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:91:13-44
492            android:enabled="false"
492-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:92:13-36
493            android:exported="false" >
493-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:93:13-37
494            <intent-filter>
494-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:95:13-98:29
495                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
495-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:96:17-83
495-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:96:25-80
496                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
496-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:97:17-82
496-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:97:25-79
497            </intent-filter>
498        </receiver>
499        <receiver
499-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:100:9-109:20
500            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
500-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:101:13-103
501            android:directBootAware="false"
501-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:102:13-44
502            android:enabled="false"
502-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:103:13-36
503            android:exported="false" >
503-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:104:13-37
504            <intent-filter>
504-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:106:13-108:29
505                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
505-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:107:17-79
505-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:107:25-76
506            </intent-filter>
507        </receiver>
508        <receiver
508-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:110:9-121:20
509            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
509-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:111:13-88
510            android:directBootAware="false"
510-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:112:13-44
511            android:enabled="false"
511-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:113:13-36
512            android:exported="false" >
512-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:114:13-37
513            <intent-filter>
513-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:116:13-120:29
514                <action android:name="android.intent.action.BOOT_COMPLETED" />
514-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:17-79
514-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:25-76
515                <action android:name="android.intent.action.TIME_SET" />
515-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:118:17-73
515-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:118:25-70
516                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
516-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:119:17-81
516-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:119:25-78
517            </intent-filter>
518        </receiver>
519        <receiver
519-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:122:9-131:20
520            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
520-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:123:13-99
521            android:directBootAware="false"
521-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:124:13-44
522            android:enabled="@bool/enable_system_alarm_service_default"
522-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:125:13-72
523            android:exported="false" >
523-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:126:13-37
524            <intent-filter>
524-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:128:13-130:29
525                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
525-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:129:17-98
525-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:129:25-95
526            </intent-filter>
527        </receiver>
528        <receiver
528-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:132:9-142:20
529            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
529-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:133:13-78
530            android:directBootAware="false"
530-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:134:13-44
531            android:enabled="true"
531-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:135:13-35
532            android:exported="true"
532-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:136:13-36
533            android:permission="android.permission.DUMP" >
533-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:137:13-57
534            <intent-filter>
534-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:139:13-141:29
535                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
535-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:140:17-88
535-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:140:25-85
536            </intent-filter>
537        </receiver>
538
539        <uses-library
539-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\701c966945a05b6cfc9f2e519b7eeb8e\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:23:9-25:40
540            android:name="android.ext.adservices"
540-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\701c966945a05b6cfc9f2e519b7eeb8e\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:24:13-50
541            android:required="false" />
541-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\701c966945a05b6cfc9f2e519b7eeb8e\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:25:13-37
542
543        <meta-data
543-->[com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\02b86cfeeba2fc323c383047854b9491\transformed\play-services-basement-18.2.0\AndroidManifest.xml:21:9-23:69
544            android:name="com.google.android.gms.version"
544-->[com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\02b86cfeeba2fc323c383047854b9491\transformed\play-services-basement-18.2.0\AndroidManifest.xml:22:13-58
545            android:value="@integer/google_play_services_version" />
545-->[com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\02b86cfeeba2fc323c383047854b9491\transformed\play-services-basement-18.2.0\AndroidManifest.xml:23:13-66
546
547        <receiver
547-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b4fc0c5c3b80165703f803a1e31a276f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
548            android:name="androidx.profileinstaller.ProfileInstallReceiver"
548-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b4fc0c5c3b80165703f803a1e31a276f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
549            android:directBootAware="false"
549-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b4fc0c5c3b80165703f803a1e31a276f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
550            android:enabled="true"
550-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b4fc0c5c3b80165703f803a1e31a276f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
551            android:exported="true"
551-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b4fc0c5c3b80165703f803a1e31a276f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
552            android:permission="android.permission.DUMP" >
552-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b4fc0c5c3b80165703f803a1e31a276f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
553            <intent-filter>
553-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b4fc0c5c3b80165703f803a1e31a276f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
554                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
554-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b4fc0c5c3b80165703f803a1e31a276f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
554-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b4fc0c5c3b80165703f803a1e31a276f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
555            </intent-filter>
556            <intent-filter>
556-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b4fc0c5c3b80165703f803a1e31a276f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
557                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
557-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b4fc0c5c3b80165703f803a1e31a276f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
557-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b4fc0c5c3b80165703f803a1e31a276f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
558            </intent-filter>
559            <intent-filter>
559-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b4fc0c5c3b80165703f803a1e31a276f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
560                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
560-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b4fc0c5c3b80165703f803a1e31a276f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
560-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b4fc0c5c3b80165703f803a1e31a276f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
561            </intent-filter>
562            <intent-filter>
562-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b4fc0c5c3b80165703f803a1e31a276f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
563                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
563-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b4fc0c5c3b80165703f803a1e31a276f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
563-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b4fc0c5c3b80165703f803a1e31a276f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
564            </intent-filter>
565        </receiver>
566
567        <service
567-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\37f7f33bfd0c5f48616a4338c6ce336d\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:28:9-34:19
568            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
568-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\37f7f33bfd0c5f48616a4338c6ce336d\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:29:13-103
569            android:exported="false" >
569-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\37f7f33bfd0c5f48616a4338c6ce336d\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:30:13-37
570            <meta-data
570-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\37f7f33bfd0c5f48616a4338c6ce336d\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:31:13-33:39
571                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
571-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\37f7f33bfd0c5f48616a4338c6ce336d\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:32:17-94
572                android:value="cct" />
572-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\37f7f33bfd0c5f48616a4338c6ce336d\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:33:17-36
573        </service>
574        <service
574-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8141aeabd6dd325d2cf83449b27069b5\transformed\transport-runtime-3.1.9\AndroidManifest.xml:26:9-30:19
575            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
575-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8141aeabd6dd325d2cf83449b27069b5\transformed\transport-runtime-3.1.9\AndroidManifest.xml:27:13-117
576            android:exported="false"
576-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8141aeabd6dd325d2cf83449b27069b5\transformed\transport-runtime-3.1.9\AndroidManifest.xml:28:13-37
577            android:permission="android.permission.BIND_JOB_SERVICE" >
577-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8141aeabd6dd325d2cf83449b27069b5\transformed\transport-runtime-3.1.9\AndroidManifest.xml:29:13-69
578        </service>
579
580        <receiver
580-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8141aeabd6dd325d2cf83449b27069b5\transformed\transport-runtime-3.1.9\AndroidManifest.xml:32:9-34:40
581            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
581-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8141aeabd6dd325d2cf83449b27069b5\transformed\transport-runtime-3.1.9\AndroidManifest.xml:33:13-132
582            android:exported="false" />
582-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8141aeabd6dd325d2cf83449b27069b5\transformed\transport-runtime-3.1.9\AndroidManifest.xml:34:13-37
583
584        <service
584-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\178194cdae2c08446775c57801d0086b\transformed\room-runtime-2.2.5\AndroidManifest.xml:25:9-28:40
585            android:name="androidx.room.MultiInstanceInvalidationService"
585-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\178194cdae2c08446775c57801d0086b\transformed\room-runtime-2.2.5\AndroidManifest.xml:26:13-74
586            android:directBootAware="true"
586-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\178194cdae2c08446775c57801d0086b\transformed\room-runtime-2.2.5\AndroidManifest.xml:27:13-43
587            android:exported="false" />
587-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\178194cdae2c08446775c57801d0086b\transformed\room-runtime-2.2.5\AndroidManifest.xml:28:13-37
588    </application>
589
590</manifest>
