import React, { useState, useEffect, useCallback } from 'react';
import { StyleSheet, Dimensions, View, ActivityIndicator, Text, TouchableOpacity, Alert } from 'react-native';
import { useRoute, useNavigation } from '@react-navigation/native';
import { WebView } from 'react-native-webview';
import { Ionicons } from '@expo/vector-icons';

// Try to import react-native-pdf with fallback
let Pdf = null;
try {
  Pdf = require('react-native-pdf').default;
} catch (error) {
  console.log('react-native-pdf not available, using WebView fallback');
}

const PdfViewerScreen = () => {
    const route = useRoute();
    const navigation = useNavigation();
    const [useWebView, setUseWebView] = useState(!Pdf); // Use WebView if native PDF not available
    const [loading, setLoading] = useState(true);
    const [currentViewerIndex, setCurrentViewerIndex] = useState(0);
    const [retryCount, setRetryCount] = useState(0);

    // Expecting the PDF source URI to be passed as a route parameter
    const { sourceUri } = route.params;

    // Check if this is a local file or remote URL
    const isLocalFile = sourceUri && (sourceUri.startsWith('file://') || sourceUri.includes('DocumentDirectory'));

    // Try multiple PDF viewing approaches for better compatibility
    const viewerOptions = isLocalFile
        ? [
            // For local files, only use direct URI (WebView can handle local files)
            sourceUri
          ]
        : [
            // For remote files, try multiple viewers
            sourceUri, // Direct PDF URL (works best for most cases)
            `https://mozilla.github.io/pdf.js/web/viewer.html?file=${encodeURIComponent(sourceUri)}`, // Mozilla PDF.js viewer
            `https://docs.google.com/gview?embedded=true&url=${encodeURIComponent(sourceUri)}` // Google Docs viewer
          ];

    const handleWebViewError = useCallback((syntheticEvent) => {
        const { nativeEvent } = syntheticEvent;
        console.error('WebView PDF Error:', nativeEvent);
        console.error('Current PDF URL:', sourceUri);
        console.error('Current viewer option:', viewerOptions[currentViewerIndex]);

        // Try next viewer option if available
        if (currentViewerIndex < viewerOptions.length - 1 && retryCount < 3) {
            console.log(`Trying alternative PDF viewer (${currentViewerIndex + 1})`);
            setCurrentViewerIndex(currentViewerIndex + 1);
            setRetryCount(retryCount + 1);
        } else {
            Alert.alert(
                'PDF Loading Error',
                `Unable to load PDF: ${nativeEvent.description || 'Unknown error'}\n\nURL: ${sourceUri}\n\nPlease check your internet connection and try again.`,
                [
                    { text: 'Go Back', onPress: () => navigation.goBack() },
                    { text: 'Retry', onPress: () => {
                        setCurrentViewerIndex(0);
                        setRetryCount(0);
                        setLoading(true);
                    }}
                ]
            );
        }
    }, [currentViewerIndex, viewerOptions.length, retryCount, sourceUri, navigation]);

    // Add timeout for loading and URL validation
    useEffect(() => {
        console.log('PDF Viewer initialized with sourceUri:', sourceUri);
        console.log('Is local file:', isLocalFile);

        // For local files, prefer native PDF viewer or show error if not available
        if (isLocalFile) {
            if (Pdf) {
                setUseWebView(false);
            } else {
                // WebView cannot load local PDF files directly
                console.warn('Local PDF file detected but native PDF viewer not available');
            }
        } else {
            // For remote files, validate URL accessibility
            const validateUrl = async () => {
                try {
                    console.log('Validating PDF URL:', sourceUri);
                    const response = await fetch(sourceUri, { method: 'HEAD' });
                    console.log('URL validation response:', response.status, response.statusText);
                    if (!response.ok) {
                        console.error('PDF URL not accessible:', response.status, response.statusText);
                        handleWebViewError({
                            nativeEvent: {
                                description: `HTTP ${response.status}: ${response.statusText}`
                            }
                        });
                    }
                } catch (error) {
                    console.error('URL validation failed:', error);
                    handleWebViewError({
                        nativeEvent: {
                            description: `Network error: ${error.message}`
                        }
                    });
                }
            };

            validateUrl();
        }

        // Set a longer timeout to prevent infinite loading
        const loadingTimeout = setTimeout(() => {
            if (loading) {
                console.log('PDF loading timeout reached, trying next viewer option');
                setLoading(false);
                handleWebViewError({ nativeEvent: { description: 'Loading timeout (30s)' } });
            }
        }, 30000); // Increased to 30 second timeout

        return () => clearTimeout(loadingTimeout);
    }, [currentViewerIndex, sourceUri, handleWebViewError, isLocalFile, loading]);

    if (!sourceUri) {
        return (
            <View style={styles.container}>
                <View style={styles.header}>
                    <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
                        <Ionicons name="arrow-back" size={24} color="#333" />
                    </TouchableOpacity>
                    <Text style={styles.title}>PDF Viewer</Text>
                </View>
                <View style={styles.errorContainer}>
                    <Text style={styles.errorText}>Error: No PDF source provided.</Text>
                </View>
            </View>
        );
    }

    // For local files when native PDF viewer is not available, use system sharing
    if (isLocalFile && !Pdf) {
        return (
            <View style={styles.container}>
                <View style={styles.header}>
                    <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
                        <Ionicons name="arrow-back" size={24} color="#333" />
                    </TouchableOpacity>
                    <Text style={styles.title}>PDF Viewer</Text>
                </View>
                <View style={styles.errorContainer}>
                    <Ionicons name="document-text-outline" size={64} color="#007AFF" />
                    <Text style={[styles.errorText, { color: '#007AFF' }]}>Open with External App</Text>
                    <Text style={styles.errorSubText}>
                        This PDF is ready to view. Tap the button below to open it with your device's PDF viewer or another app.
                    </Text>
                    <TouchableOpacity
                        style={styles.retryButton}
                        onPress={async () => {
                            try {
                                const { Sharing } = require('expo-sharing');
                                const isAvailable = await Sharing.isAvailableAsync();
                                if (isAvailable) {
                                    await Sharing.shareAsync(sourceUri, {
                                        mimeType: 'application/pdf',
                                        dialogTitle: 'Open PDF with...'
                                    });
                                } else {
                                    Alert.alert('Error', 'Sharing is not available on this device');
                                }
                            } catch (error) {
                                console.error('Error sharing PDF:', error);
                                Alert.alert('Error', 'Could not open PDF with external app');
                            }
                        }}
                    >
                        <Text style={styles.retryButtonText}>Open PDF</Text>
                    </TouchableOpacity>
                    <TouchableOpacity
                        style={[styles.retryButton, { backgroundColor: '#6c757d', marginTop: 10 }]}
                        onPress={() => navigation.goBack()}
                    >
                        <Text style={styles.retryButtonText}>Go Back</Text>
                    </TouchableOpacity>
                </View>
            </View>
        );
    }

    const handleNativePdfError = (error) => {
        console.error('Native PDF Error:', error);
        Alert.alert(
            'PDF Loading Error',
            'Failed to load PDF with native viewer. Would you like to try the web viewer?',
            [
                { text: 'Cancel', style: 'cancel' },
                { text: 'Try Web Viewer', onPress: () => setUseWebView(true) }
            ]
        );
    };

    const renderNativePdf = () => (
        <Pdf
            trustAllCerts={false}
            source={{ uri: sourceUri, cache: true }}
            onLoadComplete={(numberOfPages, filePath) => {
                console.log(`PDF loaded: ${numberOfPages} pages`);
                setLoading(false);
            }}
            onPageChanged={(page, numberOfPages) => {
                console.log(`Current page: ${page} of ${numberOfPages}`);
            }}
            onError={handleNativePdfError}
            onPressLink={(uri) => {
                console.log(`Link pressed: ${uri}`);
            }}
            style={styles.pdf}
            renderActivityIndicator={() => <ActivityIndicator color="blue" size="large" />}
            enablePaging={true}
        />
    );



    const renderWebViewPdf = () => {
        console.log(`Rendering WebView with viewer option ${currentViewerIndex}: ${viewerOptions[currentViewerIndex]}`);

        return (
            <WebView
                source={{ uri: viewerOptions[currentViewerIndex] }}
                style={styles.pdf}
                onLoadStart={() => {
                    console.log('WebView load started');
                    setLoading(true);
                }}
                onLoadEnd={() => {
                    console.log('WebView load ended');
                    setLoading(false);
                }}
                onError={handleWebViewError}
                startInLoadingState={true}
                renderLoading={() => (
                    <View style={styles.loadingContainer}>
                        <ActivityIndicator color="blue" size="large" />
                        <Text style={styles.loadingText}>Loading PDF...</Text>
                        {retryCount > 0 && (
                            <Text style={styles.retryText}>
                                Trying alternative viewer ({currentViewerIndex + 1}/{viewerOptions.length})
                            </Text>
                        )}
                    </View>
                )}
                // Additional props for better PDF handling
                allowsInlineMediaPlayback={true}
                mediaPlaybackRequiresUserAction={false}
                javaScriptEnabled={true}
                domStorageEnabled={true}
                allowFileAccess={true}
                allowUniversalAccessFromFileURLs={true}
                mixedContentMode="compatibility"
                onHttpError={(syntheticEvent) => {
                    const { nativeEvent } = syntheticEvent;
                    console.error('HTTP Error:', nativeEvent);
                    handleWebViewError(syntheticEvent);
                }}
            />
        );
    };

    return (
        <View style={styles.container}>
            <View style={styles.header}>
                <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
                    <Ionicons name="arrow-back" size={24} color="#333" />
                </TouchableOpacity>
                <Text style={styles.title}>PDF Viewer</Text>
                {Pdf && (
                    <TouchableOpacity
                        onPress={() => setUseWebView(!useWebView)}
                        style={styles.toggleButton}
                    >
                        <Ionicons
                            name={useWebView ? "document" : "globe"}
                            size={20}
                            color="#007AFF"
                        />
                    </TouchableOpacity>
                )}
            </View>

            {loading && (
                <View style={styles.loadingContainer}>
                    <ActivityIndicator color="blue" size="large" />
                    <Text style={styles.loadingText}>Loading PDF...</Text>
                </View>
            )}

            {useWebView ? renderWebViewPdf() : renderNativePdf()}
        </View>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#fff',
    },
    header: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingHorizontal: 16,
        paddingVertical: 12,
        backgroundColor: '#f8f9fa',
        borderBottomWidth: 1,
        borderBottomColor: '#e9ecef',
    },
    backButton: {
        padding: 8,
    },
    title: {
        fontSize: 18,
        fontWeight: 'bold',
        flex: 1,
        textAlign: 'center',
        marginHorizontal: 16,
    },
    toggleButton: {
        padding: 8,
    },
    pdf: {
        flex: 1,
        width: Dimensions.get('window').width,
        height: Dimensions.get('window').height,
    },
    errorContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        padding: 20,
    },
    errorText: {
        fontSize: 18,
        color: 'red',
        textAlign: 'center',
    },
    loadingContainer: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: 'rgba(255, 255, 255, 0.8)',
        zIndex: 1000,
    },
    loadingText: {
        marginTop: 10,
        fontSize: 16,
        color: '#333',
    },
    retryText: {
        marginTop: 5,
        fontSize: 14,
        color: '#666',
        fontStyle: 'italic',
    },
    errorSubText: {
        fontSize: 14,
        color: '#666',
        textAlign: 'center',
        marginTop: 10,
        paddingHorizontal: 20,
        lineHeight: 20,
    },
    retryButton: {
        backgroundColor: '#007AFF',
        paddingHorizontal: 20,
        paddingVertical: 10,
        borderRadius: 8,
        marginTop: 20,
    },
    retryButtonText: {
        color: '#fff',
        fontSize: 16,
        fontWeight: '600',
    },
});

export default PdfViewerScreen;
