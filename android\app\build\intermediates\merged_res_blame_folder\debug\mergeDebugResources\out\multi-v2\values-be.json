{"logs": [{"outputFile": "com.gokul719.snack97152fc1f368437dac54171df4ba22bd.app-mergeDebugResources-74:/values-be/values-be.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\02b86cfeeba2fc323c383047854b9491\\transformed\\play-services-basement-18.2.0\\res\\values-be\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "141", "endOffsets": "336"}, "to": {"startLines": "78", "startColumns": "4", "startOffsets": "6392", "endColumns": "145", "endOffsets": "6533"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\51b16512a656ca1125c5acd36144cb6d\\transformed\\exoplayer-core-2.18.1\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,190,257,320,397,465,564,660", "endColumns": "70,63,66,62,76,67,98,95,69", "endOffsets": "121,185,252,315,392,460,559,655,725"}, "to": {"startLines": "117,118,119,120,121,122,123,124,125", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "10048,10119,10183,10250,10313,10390,10458,10557,10653", "endColumns": "70,63,66,62,76,67,98,95,69", "endOffsets": "10114,10178,10245,10308,10385,10453,10552,10648,10718"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\491ff7541ddc657f2c55a259a03f9653\\transformed\\ui-release\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,282,376,479,565,645,734,822,904,987,1074,1146,1230,1308,1384,1469,1539", "endColumns": "92,83,93,102,85,79,88,87,81,82,86,71,83,77,75,84,69,122", "endOffsets": "193,277,371,474,560,640,729,817,899,982,1069,1141,1225,1303,1379,1464,1534,1657"}, "to": {"startLines": "67,68,89,90,91,151,152,204,205,217,218,227,228,229,230,232,233,234", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5128,5221,7729,7823,7926,12512,12592,16733,16821,17903,17986,18551,18623,18707,18785,18962,19047,19117", "endColumns": "92,83,93,102,85,79,88,87,81,82,86,71,83,77,75,84,69,122", "endOffsets": "5216,5300,7818,7921,8007,12587,12676,16816,16898,17981,18068,18618,18702,18780,18856,19042,19112,19235"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\d139f7b0159c383eb11b4ff91a7586c3\\transformed\\material-1.6.1\\res\\values-be\\values-be.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,337,419,516,632,715,782,875,952,1015,1131,1200,1259,1330,1389,1443,1564,1625,1688,1742,1815,1937,2025,2108,2230,2316,2403,2494,2561,2627,2699,2776,2860,2935,3012,3094,3170,3259,3341,3432,3528,3602,3683,3778,3832,3898,3985,4071,4133,4197,4260,4370,4477,4580,4689", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "endColumns": "12,81,96,115,82,66,92,76,62,115,68,58,70,58,53,120,60,62,53,72,121,87,82,121,85,86,90,66,65,71,76,83,74,76,81,75,88,81,90,95,73,80,94,53,65,86,85,61,63,62,109,106,102,108,79", "endOffsets": "332,414,511,627,710,777,870,947,1010,1126,1195,1254,1325,1384,1438,1559,1620,1683,1737,1810,1932,2020,2103,2225,2311,2398,2489,2556,2622,2694,2771,2855,2930,3007,3089,3165,3254,3336,3427,3523,3597,3678,3773,3827,3893,3980,4066,4128,4192,4255,4365,4472,4575,4684,4764"}, "to": {"startLines": "23,56,64,65,66,92,144,148,153,155,156,157,158,159,160,161,162,163,164,165,166,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,216", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1007,4019,4832,4929,5045,8012,11859,12279,12681,12815,12931,13000,13059,13130,13189,13243,13364,13425,13488,13542,13615,13804,13892,13975,14097,14183,14270,14361,14428,14494,14566,14643,14727,14802,14879,14961,15037,15126,15208,15299,15395,15469,15550,15645,15699,15765,15852,15938,16000,16064,16127,16237,16344,16447,17823", "endLines": "28,56,64,65,66,92,144,148,153,155,156,157,158,159,160,161,162,163,164,165,166,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,216", "endColumns": "12,81,96,115,82,66,92,76,62,115,68,58,70,58,53,120,60,62,53,72,121,87,82,121,85,86,90,66,65,71,76,83,74,76,81,75,88,81,90,95,73,80,94,53,65,86,85,61,63,62,109,106,102,108,79", "endOffsets": "1284,4096,4924,5040,5123,8074,11947,12351,12739,12926,12995,13054,13125,13184,13238,13359,13420,13483,13537,13610,13732,13887,13970,14092,14178,14265,14356,14423,14489,14561,14638,14722,14797,14874,14956,15032,15121,15203,15294,15390,15464,15545,15640,15694,15760,15847,15933,15995,16059,16122,16232,16339,16442,16551,17898"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\71743d480ebb399fb4fe6b96265e7bca\\transformed\\core-1.13.1\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,456,562,665,786", "endColumns": "97,101,99,100,105,102,120,100", "endOffsets": "148,250,350,451,557,660,781,882"}, "to": {"startLines": "57,58,59,60,61,62,63,231", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4101,4199,4301,4401,4502,4608,4711,18861", "endColumns": "97,101,99,100,105,102,120,100", "endOffsets": "4194,4296,4396,4497,4603,4706,4827,18957"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\68cda876f16b6c181c93767ccf9edd36\\transformed\\appcompat-1.7.0\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,328,444,530,635,754,834,911,1003,1097,1192,1286,1381,1475,1571,1666,1758,1850,1931,2037,2142,2240,2348,2454,2562,2735,2835", "endColumns": "119,102,115,85,104,118,79,76,91,93,94,93,94,93,95,94,91,91,80,105,104,97,107,105,107,172,99,81", "endOffsets": "220,323,439,525,630,749,829,906,998,1092,1187,1281,1376,1470,1566,1661,1753,1845,1926,2032,2137,2235,2343,2449,2557,2730,2830,2912"}, "to": {"startLines": "29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,226", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1289,1409,1512,1628,1714,1819,1938,2018,2095,2187,2281,2376,2470,2565,2659,2755,2850,2942,3034,3115,3221,3326,3424,3532,3638,3746,3919,18469", "endColumns": "119,102,115,85,104,118,79,76,91,93,94,93,94,93,95,94,91,91,80,105,104,97,107,105,107,172,99,81", "endOffsets": "1404,1507,1623,1709,1814,1933,2013,2090,2182,2276,2371,2465,2560,2654,2750,2845,2937,3029,3110,3216,3321,3419,3527,3633,3741,3914,4014,18546"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\268d4161c8b8edb1216e3f661bb7ee02\\transformed\\browser-1.6.0\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,164,272,384", "endColumns": "108,107,111,106", "endOffsets": "159,267,379,486"}, "to": {"startLines": "88,145,146,147", "startColumns": "4,4,4,4", "startOffsets": "7620,11952,12060,12172", "endColumns": "108,107,111,106", "endOffsets": "7724,12055,12167,12274"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\37f986109817451798d14c7952a3b6e1\\transformed\\react-android-0.76.9-debug\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3,4,5,6", "startColumns": "4,4,4,4,4", "startOffsets": "55,138,209,294,365", "endColumns": "82,70,84,70,66", "endOffsets": "133,204,289,360,427"}, "to": {"startLines": "69,149,150,154,167", "startColumns": "4,4,4,4,4", "startOffsets": "5305,12356,12427,12744,13737", "endColumns": "82,70,84,70,66", "endOffsets": "5383,12422,12507,12810,13799"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\263c8e8847d6991526302086ecc6e182\\transformed\\play-services-base-18.0.1\\res\\values-be\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,454,575,681,832,954,1065,1165,1323,1426,1585,1709,1858,2013,2078,2136", "endColumns": "102,157,120,105,150,121,110,99,157,102,158,123,148,154,64,57,74", "endOffsets": "295,453,574,680,831,953,1064,1164,1322,1425,1584,1708,1857,2012,2077,2135,2210"}, "to": {"startLines": "70,71,72,73,74,75,76,77,79,80,81,82,83,84,85,86,87", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5388,5495,5657,5782,5892,6047,6173,6288,6538,6700,6807,6970,7098,7251,7410,7479,7541", "endColumns": "106,161,124,109,154,125,114,103,161,106,162,127,152,158,68,61,78", "endOffsets": "5490,5652,5777,5887,6042,6168,6283,6387,6695,6802,6965,7093,7246,7405,7474,7536,7615"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\3767e343142fab2c839a998d5bfdc1ca\\transformed\\play-services-ads-22.6.0\\res\\values-be\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "199,254,304,364,431,501,604,685,799,849,976,1034,1144,1244,1288,1372,1407,1443,1496,1568,1612", "endColumns": "54,49,59,66,69,102,80,113,49,126,57,109,99,43,83,34,35,52,71,43,55", "endOffsets": "253,303,363,430,500,603,684,798,848,975,1033,1143,1243,1287,1371,1406,1442,1495,1567,1611,1667"}, "to": {"startLines": "201,202,203,206,207,208,209,210,211,212,213,214,215,219,220,221,222,223,224,225,237", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "16556,16615,16669,16903,16974,17048,17155,17240,17358,17412,17543,17605,17719,18073,18121,18209,18248,18288,18345,18421,19430", "endColumns": "58,53,63,70,73,106,84,117,53,130,61,113,103,47,87,38,39,56,75,47,59", "endOffsets": "16610,16664,16728,16969,17043,17150,17235,17353,17407,17538,17600,17714,17818,18116,18204,18243,18283,18340,18416,18464,19485"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c53b383fa8484255d5fd97643f23aa97\\transformed\\foundation-release\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,142", "endColumns": "86,102", "endOffsets": "137,240"}, "to": {"startLines": "235,236", "startColumns": "4,4", "startOffsets": "19240,19327", "endColumns": "86,102", "endOffsets": "19322,19425"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f1013ccf4ee59567949af9e71a98b092\\transformed\\exoplayer-ui-2.18.1\\res\\values-be\\values-be.xml", "from": {"startLines": "2,11,17,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,288,627,957,1040,1123,1206,1304,1402,1491,1555,1648,1742,1807,1872,1937,2005,2100,2194,2294,2371,2450,2519,2609,2702,2795,2861,2926,2979,3039,3087,3148,3221,3289,3354,3427,3492,3550,3616,3681,3747,3799,3859,3933,4007", "endLines": "10,16,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64", "endColumns": "17,12,12,82,82,82,97,97,88,63,92,93,64,64,64,67,94,93,99,76,78,68,89,92,92,65,64,52,59,47,60,72,67,64,72,64,57,65,64,65,51,59,73,73,54", "endOffsets": "283,622,952,1035,1118,1201,1299,1397,1486,1550,1643,1737,1802,1867,1932,2000,2095,2189,2289,2366,2445,2514,2604,2697,2790,2856,2921,2974,3034,3082,3143,3216,3284,3349,3422,3487,3545,3611,3676,3742,3794,3854,3928,4002,4057"}, "to": {"startLines": "2,11,17,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,338,677,8079,8162,8245,8328,8426,8524,8613,8677,8770,8864,8929,8994,9059,9127,9222,9316,9416,9493,9572,9641,9731,9824,9917,9983,10723,10776,10836,10884,10945,11018,11086,11151,11224,11289,11347,11413,11478,11544,11596,11656,11730,11804", "endLines": "10,16,22,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143", "endColumns": "17,12,12,82,82,82,97,97,88,63,92,93,64,64,64,67,94,93,99,76,78,68,89,92,92,65,64,52,59,47,60,72,67,64,72,64,57,65,64,65,51,59,73,73,54", "endOffsets": "333,672,1002,8157,8240,8323,8421,8519,8608,8672,8765,8859,8924,8989,9054,9122,9217,9311,9411,9488,9567,9636,9726,9819,9912,9978,10043,10771,10831,10879,10940,11013,11081,11146,11219,11284,11342,11408,11473,11539,11591,11651,11725,11799,11854"}}]}]}