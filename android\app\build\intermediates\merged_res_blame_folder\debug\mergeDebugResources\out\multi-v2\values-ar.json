{"logs": [{"outputFile": "com.gokul719.snack97152fc1f368437dac54171df4ba22bd.app-mergeDebugResources-74:/values-ar/values-ar.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f1013ccf4ee59567949af9e71a98b092\\transformed\\exoplayer-ui-2.18.1\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,11,19,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,349,849,1314,1393,1471,1547,1641,1733,1807,1872,1964,2054,2124,2188,2251,2320,2428,2537,2652,2718,2801,2873,2945,3037,3128,3192,3255,3308,3379,3434,3495,3553,3627,3691,3755,3815,3880,3944,4006,4072,4124,4181,4252,4323", "endLines": "10,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68", "endColumns": "17,12,12,78,77,75,93,91,73,64,91,89,69,63,62,68,107,108,114,65,82,71,71,91,90,63,62,52,70,54,60,57,73,63,63,59,64,63,61,65,51,56,70,70,55", "endOffsets": "344,844,1309,1388,1466,1542,1636,1728,1802,1867,1959,2049,2119,2183,2246,2315,2423,2532,2647,2713,2796,2868,2940,3032,3123,3187,3250,3303,3374,3429,3490,3548,3622,3686,3750,3810,3875,3939,4001,4067,4119,4176,4247,4318,4374"}, "to": {"startLines": "2,11,19,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,399,899,8536,8615,8693,8769,8863,8955,9029,9094,9186,9276,9346,9410,9473,9542,9650,9759,9874,9940,10023,10095,10167,10259,10350,10414,11173,11226,11297,11352,11413,11471,11545,11609,11673,11733,11798,11862,11924,11990,12042,12099,12170,12241", "endLines": "10,18,26,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153", "endColumns": "17,12,12,78,77,75,93,91,73,64,91,89,69,63,62,68,107,108,114,65,82,71,71,91,90,63,62,52,70,54,60,57,73,63,63,59,64,63,61,65,51,56,70,70,55", "endOffsets": "394,894,1359,8610,8688,8764,8858,8950,9024,9089,9181,9271,9341,9405,9468,9537,9645,9754,9869,9935,10018,10090,10162,10254,10345,10409,10472,11221,11292,11347,11408,11466,11540,11604,11668,11728,11793,11857,11919,11985,12037,12094,12165,12236,12292"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\37f986109817451798d14c7952a3b6e1\\transformed\\react-android-0.76.9-debug\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,206,278,346,424,491,561,639,718,799,887,965,1045,1129,1203,1281,1358,1433,1512,1584,1668,1738,1824,1893", "endColumns": "68,81,71,67,77,66,69,77,78,80,87,77,79,83,73,77,76,74,78,71,83,69,85,68,77", "endOffsets": "119,201,273,341,419,486,556,634,713,794,882,960,1040,1124,1198,1276,1353,1428,1507,1579,1663,1733,1819,1888,1966"}, "to": {"startLines": "62,76,158,165,166,170,183,184,185,238,239,242,250,253,254,255,257,258,260,262,263,265,268,270,271", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4383,5717,12696,13131,13199,13531,14500,14570,14648,18824,18905,19157,19594,19826,19910,19984,20141,20218,20372,20525,20597,20782,21002,21209,21278", "endColumns": "68,81,71,67,77,66,69,77,78,80,87,77,79,83,73,77,76,74,78,71,83,69,85,68,77", "endOffsets": "4447,5794,12763,13194,13272,13593,14565,14643,14722,18900,18988,19230,19669,19905,19979,20057,20213,20288,20446,20592,20676,20847,21083,21273,21351"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\084b6a12791f1cb8a95d16811ba661d8\\transformed\\Android-Image-Cropper-4.3.1\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12", "startColumns": "4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,154,201,255,299,363,425,498,549,605,670", "endColumns": "98,46,53,43,63,61,72,50,55,64,55", "endOffsets": "149,196,250,294,358,420,493,544,600,665,721"}, "to": {"startLines": "96,97,98,159,160,161,162,163,235,236,237", "startColumns": "4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7995,8094,8141,12768,12812,12876,12938,13011,18647,18703,18768", "endColumns": "98,46,53,43,63,61,72,50,55,64,55", "endOffsets": "8089,8136,8190,12807,12871,12933,13006,13057,18698,18763,18819"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\491ff7541ddc657f2c55a259a03f9653\\transformed\\ui-release\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,194,277,372,470,555,636,742,826,907,988,1071,1141,1220,1299,1373,1449,1523", "endColumns": "88,82,94,97,84,80,105,83,80,80,82,69,78,78,73,75,73,120", "endOffsets": "189,272,367,465,550,631,737,821,902,983,1066,1136,1215,1294,1368,1444,1518,1639"}, "to": {"startLines": "74,75,99,100,101,167,168,222,223,240,241,252,256,259,261,266,267,269", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5545,5634,8195,8290,8388,13277,13358,17542,17626,18993,19074,19756,20062,20293,20451,20852,20928,21088", "endColumns": "88,82,94,97,84,80,105,83,80,80,82,69,78,78,73,75,73,120", "endOffsets": "5629,5712,8285,8383,8468,13353,13459,17621,17702,19069,19152,19821,20136,20367,20520,20923,20997,21204"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\68cda876f16b6c181c93767ccf9edd36\\transformed\\appcompat-1.7.0\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,317,424,506,607,721,801,880,971,1064,1156,1250,1350,1443,1538,1631,1722,1816,1895,2000,2098,2196,2304,2404,2507,2662,2759", "endColumns": "107,103,106,81,100,113,79,78,90,92,91,93,99,92,94,92,90,93,78,104,97,97,107,99,102,154,96,81", "endOffsets": "208,312,419,501,602,716,796,875,966,1059,1151,1245,1345,1438,1533,1626,1717,1811,1890,1995,2093,2191,2299,2399,2502,2657,2754,2836"}, "to": {"startLines": "35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,251", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1729,1837,1941,2048,2130,2231,2345,2425,2504,2595,2688,2780,2874,2974,3067,3162,3255,3346,3440,3519,3624,3722,3820,3928,4028,4131,4286,19674", "endColumns": "107,103,106,81,100,113,79,78,90,92,91,93,99,92,94,92,90,93,78,104,97,97,107,99,102,154,96,81", "endOffsets": "1832,1936,2043,2125,2226,2340,2420,2499,2590,2683,2775,2869,2969,3062,3157,3250,3341,3435,3514,3619,3717,3815,3923,4023,4126,4281,4378,19751"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\02b86cfeeba2fc323c383047854b9491\\transformed\\play-services-basement-18.2.0\\res\\values-ar\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "125", "endOffsets": "320"}, "to": {"startLines": "85", "startColumns": "4", "startOffsets": "6753", "endColumns": "129", "endOffsets": "6878"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\71743d480ebb399fb4fe6b96265e7bca\\transformed\\core-1.13.1\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,148,250,345,448,551,653,767", "endColumns": "92,101,94,102,102,101,113,100", "endOffsets": "143,245,340,443,546,648,762,863"}, "to": {"startLines": "64,65,66,67,68,69,70,264", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4536,4629,4731,4826,4929,5032,5134,20681", "endColumns": "92,101,94,102,102,101,113,100", "endOffsets": "4624,4726,4821,4924,5027,5129,5243,20777"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\263c8e8847d6991526302086ecc6e182\\transformed\\play-services-base-18.0.1\\res\\values-ar\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,293,433,551,652,786,910,1017,1115,1248,1348,1494,1612,1747,1889,1949,2011", "endColumns": "99,139,117,100,133,123,106,97,132,99,145,117,134,141,59,61,79", "endOffsets": "292,432,550,651,785,909,1016,1114,1247,1347,1493,1611,1746,1888,1948,2010,2090"}, "to": {"startLines": "77,78,79,80,81,82,83,84,86,87,88,89,90,91,92,93,94", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5799,5903,6047,6169,6274,6412,6540,6651,6883,7020,7124,7274,7396,7535,7681,7745,7811", "endColumns": "103,143,121,104,137,127,110,101,136,103,149,121,138,145,63,65,83", "endOffsets": "5898,6042,6164,6269,6407,6535,6646,6748,7015,7119,7269,7391,7530,7676,7740,7806,7890"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\3767e343142fab2c839a998d5bfdc1ca\\transformed\\play-services-ads-22.6.0\\res\\values-ar\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "199,240,288,342,406,474,573,641,741,794,908,965,1077,1162,1200,1279,1311,1342,1385,1453,1493", "endColumns": "40,47,53,63,67,98,67,99,52,113,56,111,84,37,78,31,30,42,67,39,55", "endOffsets": "239,287,341,405,473,572,640,740,793,907,964,1076,1161,1199,1278,1310,1341,1384,1452,1492,1548"}, "to": {"startLines": "219,220,221,224,225,226,227,228,229,230,231,232,233,243,244,245,246,247,248,249,274", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "17387,17432,17484,17707,17775,17847,17950,18022,18126,18183,18301,18362,18478,19235,19277,19360,19396,19431,19478,19550,21526", "endColumns": "44,51,57,67,71,102,71,103,56,117,60,115,88,41,82,35,34,46,71,43,59", "endOffsets": "17427,17479,17537,17770,17842,17945,18017,18121,18178,18296,18357,18473,18562,19272,19355,19391,19426,19473,19545,19589,21581"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c53b383fa8484255d5fd97643f23aa97\\transformed\\foundation-release\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,139", "endColumns": "83,85", "endOffsets": "134,220"}, "to": {"startLines": "272,273", "startColumns": "4,4", "startOffsets": "21356,21440", "endColumns": "83,85", "endOffsets": "21435,21521"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\268d4161c8b8edb1216e3f661bb7ee02\\transformed\\browser-1.6.0\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,253,361", "endColumns": "99,97,107,101", "endOffsets": "150,248,356,458"}, "to": {"startLines": "95,155,156,157", "startColumns": "4,4,4,4", "startOffsets": "7895,12388,12486,12594", "endColumns": "99,97,107,101", "endOffsets": "7990,12481,12589,12691"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\51b16512a656ca1125c5acd36144cb6d\\transformed\\exoplayer-core-2.18.1\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,120,179,246,308,390,471,572,667", "endColumns": "64,58,66,61,81,80,100,94,83", "endOffsets": "115,174,241,303,385,466,567,662,746"}, "to": {"startLines": "127,128,129,130,131,132,133,134,135", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "10477,10542,10601,10668,10730,10812,10893,10994,11089", "endColumns": "64,58,66,61,81,80,100,94,83", "endOffsets": "10537,10596,10663,10725,10807,10888,10989,11084,11168"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\d139f7b0159c383eb11b4ff91a7586c3\\transformed\\material-1.6.1\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,420,504,605,724,801,864,955,1024,1091,1191,1256,1317,1385,1447,1505,1619,1679,1740,1797,1870,1993,2074,2154,2272,2353,2434,2523,2590,2656,2734,2814,2898,2970,3044,3117,3187,3278,3349,3439,3534,3608,3691,3784,3833,3902,3988,4073,4135,4199,4262,4371,4463,4560,4653", "endLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63", "endColumns": "12,83,100,118,76,62,90,68,66,99,64,60,67,61,57,113,59,60,56,72,122,80,79,117,80,80,88,66,65,77,79,83,71,73,72,69,90,70,89,94,73,82,92,48,68,85,84,61,63,62,108,91,96,92,79", "endOffsets": "415,499,600,719,796,859,950,1019,1086,1186,1251,1312,1380,1442,1500,1614,1674,1735,1792,1865,1988,2069,2149,2267,2348,2429,2518,2585,2651,2729,2809,2893,2965,3039,3112,3182,3273,3344,3434,3529,3603,3686,3779,3828,3897,3983,4068,4130,4194,4257,4366,4458,4555,4648,4728"}, "to": {"startLines": "27,63,71,72,73,102,154,164,169,171,172,173,174,175,176,177,178,179,180,181,182,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,234", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1364,4452,5248,5349,5468,8473,12297,13062,13464,13598,13698,13763,13824,13892,13954,14012,14126,14186,14247,14304,14377,14727,14808,14888,15006,15087,15168,15257,15324,15390,15468,15548,15632,15704,15778,15851,15921,16012,16083,16173,16268,16342,16425,16518,16567,16636,16722,16807,16869,16933,16996,17105,17197,17294,18567", "endLines": "34,63,71,72,73,102,154,164,169,171,172,173,174,175,176,177,178,179,180,181,182,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,234", "endColumns": "12,83,100,118,76,62,90,68,66,99,64,60,67,61,57,113,59,60,56,72,122,80,79,117,80,80,88,66,65,77,79,83,71,73,72,69,90,70,89,94,73,82,92,48,68,85,84,61,63,62,108,91,96,92,79", "endOffsets": "1724,4531,5344,5463,5540,8531,12383,13126,13526,13693,13758,13819,13887,13949,14007,14121,14181,14242,14299,14372,14495,14803,14883,15001,15082,15163,15252,15319,15385,15463,15543,15627,15699,15773,15846,15916,16007,16078,16168,16263,16337,16420,16513,16562,16631,16717,16802,16864,16928,16991,17100,17192,17289,17382,18642"}}]}]}