{"logs": [{"outputFile": "com.gokul719.snack97152fc1f368437dac54171df4ba22bd.app-mergeDebugResources-74:/values-tr/values-tr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\268d4161c8b8edb1216e3f661bb7ee02\\transformed\\browser-1.6.0\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,261,368", "endColumns": "99,105,106,105", "endOffsets": "150,256,363,469"}, "to": {"startLines": "83,143,144,145", "startColumns": "4,4,4,4", "startOffsets": "7213,11707,11813,11920", "endColumns": "99,105,106,105", "endOffsets": "7308,11808,11915,12021"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\51b16512a656ca1125c5acd36144cb6d\\transformed\\exoplayer-core-2.18.1\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,132,191,256,317,397,469,559,655", "endColumns": "76,58,64,60,79,71,89,95,75", "endOffsets": "127,186,251,312,392,464,554,650,726"}, "to": {"startLines": "115,116,117,118,119,120,121,122,123", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "9830,9907,9966,10031,10092,10172,10244,10334,10430", "endColumns": "76,58,64,60,79,71,89,95,75", "endOffsets": "9902,9961,10026,10087,10167,10239,10329,10425,10501"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\d139f7b0159c383eb11b4ff91a7586c3\\transformed\\material-1.6.1\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,220,297,393,505,587,651,742,819,880,971,1034,1093,1162,1225,1279,1387,1445,1507,1561,1634,1755,1839,1930,2040,2117,2193,2280,2347,2413,2483,2560,2643,2714,2789,2867,2938,3023,3112,3207,3300,3372,3444,3540,3592,3659,3743,3833,3895,3959,4022,4116,4212,4301,4398", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,76,95,111,81,63,90,76,60,90,62,58,68,62,53,107,57,61,53,72,120,83,90,109,76,75,86,66,65,69,76,82,70,74,77,70,84,88,94,92,71,71,95,51,66,83,89,61,63,62,93,95,88,96,78", "endOffsets": "215,292,388,500,582,646,737,814,875,966,1029,1088,1157,1220,1274,1382,1440,1502,1556,1629,1750,1834,1925,2035,2112,2188,2275,2342,2408,2478,2555,2638,2709,2784,2862,2933,3018,3107,3202,3295,3367,3439,3535,3587,3654,3738,3828,3890,3954,4017,4111,4207,4296,4393,4472"}, "to": {"startLines": "19,51,59,60,61,90,142,152,157,159,160,161,162,163,164,165,166,167,168,169,170,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,222", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "702,3628,4418,4514,4626,7799,11616,12405,12818,12950,13041,13104,13163,13232,13295,13349,13457,13515,13577,13631,13704,14049,14133,14224,14334,14411,14487,14574,14641,14707,14777,14854,14937,15008,15083,15161,15232,15317,15406,15501,15594,15666,15738,15834,15886,15953,16037,16127,16189,16253,16316,16410,16506,16595,17944", "endLines": "22,51,59,60,61,90,142,152,157,159,160,161,162,163,164,165,166,167,168,169,170,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,222", "endColumns": "12,76,95,111,81,63,90,76,60,90,62,58,68,62,53,107,57,61,53,72,120,83,90,109,76,75,86,66,65,69,76,82,70,74,77,70,84,88,94,92,71,71,95,51,66,83,89,61,63,62,93,95,88,96,78", "endOffsets": "862,3700,4509,4621,4703,7858,11702,12477,12874,13036,13099,13158,13227,13290,13344,13452,13510,13572,13626,13699,13820,14128,14219,14329,14406,14482,14569,14636,14702,14772,14849,14932,15003,15078,15156,15227,15312,15401,15496,15589,15661,15733,15829,15881,15948,16032,16122,16184,16248,16311,16405,16501,16590,16687,18018"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\02b86cfeeba2fc323c383047854b9491\\transformed\\play-services-basement-18.2.0\\res\\values-tr\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "142", "endOffsets": "337"}, "to": {"startLines": "73", "startColumns": "4", "startOffsets": "5978", "endColumns": "146", "endOffsets": "6120"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\3767e343142fab2c839a998d5bfdc1ca\\transformed\\play-services-ads-22.6.0\\res\\values-tr\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "199,241,288,345,411,479,591,668,782,835,961,1014,1135,1227,1267,1358,1394,1428,1480,1566,1606", "endColumns": "41,46,56,65,67,111,76,113,52,125,52,120,91,39,90,35,33,51,85,39,55", "endOffsets": "240,287,344,410,478,590,667,781,834,960,1013,1134,1226,1266,1357,1393,1427,1479,1565,1605,1661"}, "to": {"startLines": "207,208,209,212,213,214,215,216,217,218,219,220,221,229,230,231,232,233,234,235,260", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "16692,16738,16789,17022,17092,17164,17280,17361,17479,17536,17666,17723,17848,18493,18537,18632,18672,18710,18766,18856,20832", "endColumns": "45,50,60,69,71,115,80,117,56,129,56,124,95,43,94,39,37,55,89,43,59", "endOffsets": "16733,16784,16845,17087,17159,17275,17356,17474,17531,17661,17718,17843,17939,18532,18627,18667,18705,18761,18851,18895,20887"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\263c8e8847d6991526302086ecc6e182\\transformed\\play-services-base-18.0.1\\res\\values-tr\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,299,450,582,683,826,952,1075,1177,1345,1448,1601,1731,1872,2035,2093,2153", "endColumns": "105,150,131,100,142,125,122,101,167,102,152,129,140,162,57,59,75", "endOffsets": "298,449,581,682,825,951,1074,1176,1344,1447,1600,1730,1871,2034,2092,2152,2228"}, "to": {"startLines": "65,66,67,68,69,70,71,72,74,75,76,77,78,79,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4962,5072,5227,5363,5468,5615,5745,5872,6125,6297,6404,6561,6695,6840,7007,7069,7133", "endColumns": "109,154,135,104,146,129,126,105,171,106,156,133,144,166,61,63,79", "endOffsets": "5067,5222,5358,5463,5610,5740,5867,5973,6292,6399,6556,6690,6835,7002,7064,7128,7208"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\084b6a12791f1cb8a95d16811ba661d8\\transformed\\Android-Image-Cropper-4.3.1\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,161,208,262,307,378,447,513,570", "endColumns": "105,46,53,44,70,68,65,56,66", "endOffsets": "156,203,257,302,373,442,508,565,632"}, "to": {"startLines": "84,85,86,147,148,149,150,151,223", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "7313,7419,7466,12097,12142,12213,12282,12348,18023", "endColumns": "105,46,53,44,70,68,65,56,66", "endOffsets": "7414,7461,7515,12137,12208,12277,12343,12400,18085"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c53b383fa8484255d5fd97643f23aa97\\transformed\\foundation-release\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,139", "endColumns": "83,86", "endOffsets": "134,221"}, "to": {"startLines": "258,259", "startColumns": "4,4", "startOffsets": "20661,20745", "endColumns": "83,86", "endOffsets": "20740,20827"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\491ff7541ddc657f2c55a259a03f9653\\transformed\\ui-release\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,282,377,477,561,644,744,832,916,996,1084,1155,1229,1304,1376,1454,1522", "endColumns": "92,83,94,99,83,82,99,87,83,79,87,70,73,74,71,77,67,117", "endOffsets": "193,277,372,472,556,639,739,827,911,991,1079,1150,1224,1299,1371,1449,1517,1635"}, "to": {"startLines": "62,63,87,88,89,155,156,210,211,226,227,238,242,245,247,252,253,255", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4708,4801,7520,7615,7715,12635,12718,16850,16938,18255,18335,19063,19379,19616,19765,20166,20244,20391", "endColumns": "92,83,94,99,83,82,99,87,83,79,87,70,73,74,71,77,67,117", "endOffsets": "4796,4880,7610,7710,7794,12713,12813,16933,17017,18330,18418,19129,19448,19686,19832,20239,20307,20504"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f1013ccf4ee59567949af9e71a98b092\\transformed\\exoplayer-ui-2.18.1\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,472,652,741,834,909,994,1080,1155,1221,1306,1392,1460,1522,1582,1651,1768,1880,1997,2066,2150,2220,2296,2391,2490,2555,2619,2672,2730,2778,2839,2903,2970,3032,3098,3160,3217,3281,3346,3412,3464,3524,3598,3672", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,88,92,74,84,85,74,65,84,85,67,61,59,68,116,111,116,68,83,69,75,94,98,64,63,52,57,47,60,63,66,61,65,61,56,63,64,65,51,59,73,73,56", "endOffsets": "280,467,647,736,829,904,989,1075,1150,1216,1301,1387,1455,1517,1577,1646,1763,1875,1992,2061,2145,2215,2291,2386,2485,2550,2614,2667,2725,2773,2834,2898,2965,3027,3093,3155,3212,3276,3341,3407,3459,3519,3593,3667,3724"}, "to": {"startLines": "2,11,15,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,335,522,7863,7952,8045,8120,8205,8291,8366,8432,8517,8603,8671,8733,8793,8862,8979,9091,9208,9277,9361,9431,9507,9602,9701,9766,10506,10559,10617,10665,10726,10790,10857,10919,10985,11047,11104,11168,11233,11299,11351,11411,11485,11559", "endLines": "10,14,18,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141", "endColumns": "17,12,12,88,92,74,84,85,74,65,84,85,67,61,59,68,116,111,116,68,83,69,75,94,98,64,63,52,57,47,60,63,66,61,65,61,56,63,64,65,51,59,73,73,56", "endOffsets": "330,517,697,7947,8040,8115,8200,8286,8361,8427,8512,8598,8666,8728,8788,8857,8974,9086,9203,9272,9356,9426,9502,9597,9696,9761,9825,10554,10612,10660,10721,10785,10852,10914,10980,11042,11099,11163,11228,11294,11346,11406,11480,11554,11611"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\68cda876f16b6c181c93767ccf9edd36\\transformed\\appcompat-1.7.0\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,318,430,515,621,741,821,896,987,1080,1172,1266,1366,1459,1561,1656,1747,1838,1917,2024,2128,2224,2331,2434,2543,2699,2797", "endColumns": "113,98,111,84,105,119,79,74,90,92,91,93,99,92,101,94,90,90,78,106,103,95,106,102,108,155,97,79", "endOffsets": "214,313,425,510,616,736,816,891,982,1075,1167,1261,1361,1454,1556,1651,1742,1833,1912,2019,2123,2219,2326,2429,2538,2694,2792,2872"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,237", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "867,981,1080,1192,1277,1383,1503,1583,1658,1749,1842,1934,2028,2128,2221,2323,2418,2509,2600,2679,2786,2890,2986,3093,3196,3305,3461,18983", "endColumns": "113,98,111,84,105,119,79,74,90,92,91,93,99,92,101,94,90,90,78,106,103,95,106,102,108,155,97,79", "endOffsets": "976,1075,1187,1272,1378,1498,1578,1653,1744,1837,1929,2023,2123,2216,2318,2413,2504,2595,2674,2781,2885,2981,3088,3191,3300,3456,3554,19058"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\37f986109817451798d14c7952a3b6e1\\transformed\\react-android-0.76.9-debug\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,201,272,342,425,496,563,640,720,805,885,955,1038,1123,1198,1283,1369,1446,1520,1591,1678,1748,1827,1902", "endColumns": "68,76,70,69,82,70,66,76,79,84,79,69,82,84,74,84,85,76,73,70,86,69,78,74,76", "endOffsets": "119,196,267,337,420,491,558,635,715,800,880,950,1033,1118,1193,1278,1364,1441,1515,1586,1673,1743,1822,1897,1974"}, "to": {"startLines": "50,64,146,153,154,158,171,172,173,224,225,228,236,239,240,241,243,244,246,248,249,251,254,256,257", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3559,4885,12026,12482,12552,12879,13825,13892,13969,18090,18175,18423,18900,19134,19219,19294,19453,19539,19691,19837,19908,20096,20312,20509,20584", "endColumns": "68,76,70,69,82,70,66,76,79,84,79,69,82,84,74,84,85,76,73,70,86,69,78,74,76", "endOffsets": "3623,4957,12092,12547,12630,12945,13887,13964,14044,18170,18250,18488,18978,19214,19289,19374,19534,19611,19760,19903,19990,20161,20386,20579,20656"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\71743d480ebb399fb4fe6b96265e7bca\\transformed\\core-1.13.1\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,352,449,551,657,768", "endColumns": "96,101,97,96,101,105,110,100", "endOffsets": "147,249,347,444,546,652,763,864"}, "to": {"startLines": "52,53,54,55,56,57,58,250", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3705,3802,3904,4002,4099,4201,4307,19995", "endColumns": "96,101,97,96,101,105,110,100", "endOffsets": "3797,3899,3997,4094,4196,4302,4413,20091"}}]}]}