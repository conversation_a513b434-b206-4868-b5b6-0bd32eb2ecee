{"logs": [{"outputFile": "com.gokul719.snack97152fc1f368437dac54171df4ba22bd.app-mergeDebugResources-74:/values-b+sr+Latn/values-b+sr+Latn.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\268d4161c8b8edb1216e3f661bb7ee02\\transformed\\browser-1.6.0\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,166,266,379", "endColumns": "110,99,112,97", "endOffsets": "161,261,374,472"}, "to": {"startLines": "84,141,142,143", "startColumns": "4,4,4,4", "startOffsets": "7294,11622,11722,11835", "endColumns": "110,99,112,97", "endOffsets": "7400,11717,11830,11928"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f1013ccf4ee59567949af9e71a98b092\\transformed\\exoplayer-ui-2.18.1\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,11,16,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,287,556,817,899,982,1064,1153,1244,1314,1381,1475,1570,1638,1702,1765,1837,1946,2060,2171,2247,2335,2409,2480,2572,2665,2732,2797,2850,2908,2956,3017,3083,3147,3210,3275,3339,3400,3466,3531,3597,3649,3711,3787,3863", "endLines": "10,15,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62", "endColumns": "17,12,12,81,82,81,88,90,69,66,93,94,67,63,62,71,108,113,110,75,87,73,70,91,92,66,64,52,57,47,60,65,63,62,64,63,60,65,64,65,51,61,75,75,55", "endOffsets": "282,551,812,894,977,1059,1148,1239,1309,1376,1470,1565,1633,1697,1760,1832,1941,2055,2166,2242,2330,2404,2475,2567,2660,2727,2792,2845,2903,2951,3012,3078,3142,3205,3270,3334,3395,3461,3526,3592,3644,3706,3782,3858,3914"}, "to": {"startLines": "2,11,16,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,337,606,7755,7837,7920,8002,8091,8182,8252,8319,8413,8508,8576,8640,8703,8775,8884,8998,9109,9185,9273,9347,9418,9510,9603,9670,10407,10460,10518,10566,10627,10693,10757,10820,10885,10949,11010,11076,11141,11207,11259,11321,11397,11473", "endLines": "10,15,20,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139", "endColumns": "17,12,12,81,82,81,88,90,69,66,93,94,67,63,62,71,108,113,110,75,87,73,70,91,92,66,64,52,57,47,60,65,63,62,64,63,60,65,64,65,51,61,75,75,55", "endOffsets": "332,601,862,7832,7915,7997,8086,8177,8247,8314,8408,8503,8571,8635,8698,8770,8879,8993,9104,9180,9268,9342,9413,9505,9598,9665,9730,10455,10513,10561,10622,10688,10752,10815,10880,10944,11005,11071,11136,11202,11254,11316,11392,11468,11524"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c53b383fa8484255d5fd97643f23aa97\\transformed\\foundation-release\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,145", "endColumns": "89,91", "endOffsets": "140,232"}, "to": {"startLines": "227,228", "startColumns": "4,4", "startOffsets": "18467,18557", "endColumns": "89,91", "endOffsets": "18552,18644"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\02b86cfeeba2fc323c383047854b9491\\transformed\\play-services-basement-18.2.0\\res\\values-b+sr+Latn\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "202", "endColumns": "125", "endOffsets": "327"}, "to": {"startLines": "74", "startColumns": "4", "startOffsets": "6116", "endColumns": "129", "endOffsets": "6241"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\3767e343142fab2c839a998d5bfdc1ca\\transformed\\play-services-ads-22.6.0\\res\\values-b+sr+Latn\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "206,250,297,353,418,486,598,682,789,844,953,1010,1115,1201,1242,1332,1368,1401,1460,1548,1588", "endColumns": "43,46,55,64,67,111,83,106,54,108,56,104,85,40,89,35,32,58,87,39,55", "endOffsets": "249,296,352,417,485,597,681,788,843,952,1009,1114,1200,1241,1331,1367,1400,1459,1547,1587,1643"}, "to": {"startLines": "193,194,195,198,199,200,201,202,203,204,205,206,207,211,212,213,214,215,216,217,229", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15822,15870,15921,16158,16227,16299,16415,16503,16614,16673,16786,16847,16956,17291,17336,17430,17470,17507,17570,17662,18649", "endColumns": "47,50,59,68,71,115,87,110,58,112,60,108,89,44,93,39,36,62,91,43,59", "endOffsets": "15865,15916,15976,16222,16294,16410,16498,16609,16668,16781,16842,16951,17041,17331,17425,17465,17502,17565,17657,17701,18704"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\d139f7b0159c383eb11b4ff91a7586c3\\transformed\\material-1.6.1\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,279,359,453,585,666,732,825,893,956,1059,1125,1181,1252,1312,1366,1478,1535,1596,1650,1726,1851,1938,2021,2130,2212,2295,2383,2450,2516,2590,2668,2757,2833,2909,2984,3056,3146,3219,3311,3407,3479,3555,3651,3704,3771,3858,3945,4007,4071,4134,4239,4343,4439,4546", "endLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "12,79,93,131,80,65,92,67,62,102,65,55,70,59,53,111,56,60,53,75,124,86,82,108,81,82,87,66,65,73,77,88,75,75,74,71,89,72,91,95,71,75,95,52,66,86,86,61,63,62,104,103,95,106,79", "endOffsets": "274,354,448,580,661,727,820,888,951,1054,1120,1176,1247,1307,1361,1473,1530,1591,1645,1721,1846,1933,2016,2125,2207,2290,2378,2445,2511,2585,2663,2752,2828,2904,2979,3051,3141,3214,3306,3402,3474,3550,3646,3699,3766,3853,3940,4002,4066,4129,4234,4338,4434,4541,4621"}, "to": {"startLines": "21,53,61,62,63,88,140,144,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,208", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "867,3822,4628,4722,4854,7689,11529,11933,12169,12232,12335,12401,12457,12528,12588,12642,12754,12811,12872,12926,13002,13127,13214,13297,13406,13488,13571,13659,13726,13792,13866,13944,14033,14109,14185,14260,14332,14422,14495,14587,14683,14755,14831,14927,14980,15047,15134,15221,15283,15347,15410,15515,15619,15715,17046", "endLines": "25,53,61,62,63,88,140,144,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,208", "endColumns": "12,79,93,131,80,65,92,67,62,102,65,55,70,59,53,111,56,60,53,75,124,86,82,108,81,82,87,66,65,73,77,88,75,75,74,71,89,72,91,95,71,75,95,52,66,86,86,61,63,62,104,103,95,106,79", "endOffsets": "1086,3897,4717,4849,4930,7750,11617,11996,12227,12330,12396,12452,12523,12583,12637,12749,12806,12867,12921,12997,13122,13209,13292,13401,13483,13566,13654,13721,13787,13861,13939,14028,14104,14180,14255,14327,14417,14490,14582,14678,14750,14826,14922,14975,15042,15129,15216,15278,15342,15405,15510,15614,15710,15817,17121"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\491ff7541ddc657f2c55a259a03f9653\\transformed\\ui-release\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,289,386,487,573,650,741,833,918,998,1083,1156,1233,1312,1389,1468,1538", "endColumns": "96,86,96,100,85,76,90,91,84,79,84,72,76,78,76,78,69,117", "endOffsets": "197,284,381,482,568,645,736,828,913,993,1078,1151,1228,1307,1384,1463,1533,1651"}, "to": {"startLines": "64,65,85,86,87,145,146,196,197,209,210,219,220,221,222,224,225,226", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4935,5032,7405,7502,7603,12001,12078,15981,16073,17126,17206,17793,17866,17943,18022,18200,18279,18349", "endColumns": "96,86,96,100,85,76,90,91,84,79,84,72,76,78,76,78,69,117", "endOffsets": "5027,5114,7497,7598,7684,12073,12164,16068,16153,17201,17286,17861,17938,18017,18094,18274,18344,18462"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\263c8e8847d6991526302086ecc6e182\\transformed\\play-services-base-18.0.1\\res\\values-b+sr+Latn\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "200,303,457,580,686,836,959,1067,1165,1310,1413,1569,1692,1837,1976,2040,2101", "endColumns": "102,153,122,105,149,122,107,97,144,102,155,122,144,138,63,60,75", "endOffsets": "302,456,579,685,835,958,1066,1164,1309,1412,1568,1691,1836,1975,2039,2100,2176"}, "to": {"startLines": "66,67,68,69,70,71,72,73,75,76,77,78,79,80,81,82,83", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5119,5226,5384,5511,5621,5775,5902,6014,6246,6395,6502,6662,6789,6938,7081,7149,7214", "endColumns": "106,157,126,109,153,126,111,101,148,106,159,126,148,142,67,64,79", "endOffsets": "5221,5379,5506,5616,5770,5897,6009,6111,6390,6497,6657,6784,6933,7076,7144,7209,7289"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\71743d480ebb399fb4fe6b96265e7bca\\transformed\\core-1.13.1\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,352,456,560,665,781", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "148,250,347,451,555,660,776,877"}, "to": {"startLines": "54,55,56,57,58,59,60,223", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3902,4000,4102,4199,4303,4407,4512,18099", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "3995,4097,4194,4298,4402,4507,4623,18195"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\68cda876f16b6c181c93767ccf9edd36\\transformed\\appcompat-1.7.0\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,419,505,609,731,816,898,989,1082,1177,1271,1371,1464,1559,1664,1755,1846,1932,2037,2143,2246,2353,2462,2569,2739,2836", "endColumns": "106,100,105,85,103,121,84,81,90,92,94,93,99,92,94,104,90,90,85,104,105,102,106,108,106,169,96,86", "endOffsets": "207,308,414,500,604,726,811,893,984,1077,1172,1266,1366,1459,1554,1659,1750,1841,1927,2032,2138,2241,2348,2457,2564,2734,2831,2918"}, "to": {"startLines": "26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,218", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1091,1198,1299,1405,1491,1595,1717,1802,1884,1975,2068,2163,2257,2357,2450,2545,2650,2741,2832,2918,3023,3129,3232,3339,3448,3555,3725,17706", "endColumns": "106,100,105,85,103,121,84,81,90,92,94,93,99,92,94,104,90,90,85,104,105,102,106,108,106,169,96,86", "endOffsets": "1193,1294,1400,1486,1590,1712,1797,1879,1970,2063,2158,2252,2352,2445,2540,2645,2736,2827,2913,3018,3124,3227,3334,3443,3550,3720,3817,17788"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\51b16512a656ca1125c5acd36144cb6d\\transformed\\exoplayer-core-2.18.1\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,191,256,328,407,480,568,652", "endColumns": "74,60,64,71,78,72,87,83,74", "endOffsets": "125,186,251,323,402,475,563,647,722"}, "to": {"startLines": "113,114,115,116,117,118,119,120,121", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "9735,9810,9871,9936,10008,10087,10160,10248,10332", "endColumns": "74,60,64,71,78,72,87,83,74", "endOffsets": "9805,9866,9931,10003,10082,10155,10243,10327,10402"}}]}]}