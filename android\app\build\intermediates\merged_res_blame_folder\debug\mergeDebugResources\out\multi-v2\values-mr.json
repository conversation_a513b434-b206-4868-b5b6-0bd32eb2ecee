{"logs": [{"outputFile": "com.gokul719.snack97152fc1f368437dac54171df4ba22bd.app-mergeDebugResources-74:/values-mr/values-mr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\d139f7b0159c383eb11b4ff91a7586c3\\transformed\\material-1.6.1\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,214,297,397,513,595,658,749,814,873,961,1023,1083,1150,1213,1267,1381,1438,1499,1553,1623,1742,1823,1908,2013,2090,2167,2253,2320,2386,2456,2534,2621,2691,2767,2838,2907,3003,3077,3175,3271,3345,3415,3517,3572,3639,3726,3819,3882,3946,4009,4109,4212,4306,4410", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,82,99,115,81,62,90,64,58,87,61,59,66,62,53,113,56,60,53,69,118,80,84,104,76,76,85,66,65,69,77,86,69,75,70,68,95,73,97,95,73,69,101,54,66,86,92,62,63,62,99,102,93,103,77", "endOffsets": "209,292,392,508,590,653,744,809,868,956,1018,1078,1145,1208,1262,1376,1433,1494,1548,1618,1737,1818,1903,2008,2085,2162,2248,2315,2381,2451,2529,2616,2686,2762,2833,2902,2998,3072,3170,3266,3340,3410,3512,3567,3634,3721,3814,3877,3941,4004,4104,4207,4301,4405,4483"}, "to": {"startLines": "19,51,59,60,61,87,139,144,149,151,152,153,154,155,156,157,158,159,160,161,162,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,214", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "721,3639,4454,4554,4670,7549,11324,11798,12187,12313,12401,12463,12523,12590,12653,12707,12821,12878,12939,12993,13063,13399,13480,13565,13670,13747,13824,13910,13977,14043,14113,14191,14278,14348,14424,14495,14564,14660,14734,14832,14928,15002,15072,15174,15229,15296,15383,15476,15539,15603,15666,15766,15869,15963,17299", "endLines": "22,51,59,60,61,87,139,144,149,151,152,153,154,155,156,157,158,159,160,161,162,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,214", "endColumns": "12,82,99,115,81,62,90,64,58,87,61,59,66,62,53,113,56,60,53,69,118,80,84,104,76,76,85,66,65,69,77,86,69,75,70,68,95,73,97,95,73,69,101,54,66,86,92,62,63,62,99,102,93,103,77", "endOffsets": "875,3717,4549,4665,4747,7607,11410,11858,12241,12396,12458,12518,12585,12648,12702,12816,12873,12934,12988,13058,13177,13475,13560,13665,13742,13819,13905,13972,14038,14108,14186,14273,14343,14419,14490,14559,14655,14729,14827,14923,14997,15067,15169,15224,15291,15378,15471,15534,15598,15661,15761,15864,15958,16062,17372"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\68cda876f16b6c181c93767ccf9edd36\\transformed\\appcompat-1.7.0\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,322,429,519,620,732,810,887,978,1071,1164,1261,1361,1454,1549,1643,1734,1825,1905,2012,2113,2210,2319,2421,2535,2692,2795", "endColumns": "110,105,106,89,100,111,77,76,90,92,92,96,99,92,94,93,90,90,79,106,100,96,108,101,113,156,102,79", "endOffsets": "211,317,424,514,615,727,805,882,973,1066,1159,1256,1356,1449,1544,1638,1729,1820,1900,2007,2108,2205,2314,2416,2530,2687,2790,2870"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,228", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "880,991,1097,1204,1294,1395,1507,1585,1662,1753,1846,1939,2036,2136,2229,2324,2418,2509,2600,2680,2787,2888,2985,3094,3196,3310,3467,18262", "endColumns": "110,105,106,89,100,111,77,76,90,92,92,96,99,92,94,93,90,90,79,106,100,96,108,101,113,156,102,79", "endOffsets": "986,1092,1199,1289,1390,1502,1580,1657,1748,1841,1934,2031,2131,2224,2319,2413,2504,2595,2675,2782,2883,2980,3089,3191,3305,3462,3565,18337"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\268d4161c8b8edb1216e3f661bb7ee02\\transformed\\browser-1.6.0\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,156,257,368", "endColumns": "100,100,110,101", "endOffsets": "151,252,363,465"}, "to": {"startLines": "83,140,141,142", "startColumns": "4,4,4,4", "startOffsets": "7166,11415,11516,11627", "endColumns": "100,100,110,101", "endOffsets": "7262,11511,11622,11724"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\263c8e8847d6991526302086ecc6e182\\transformed\\play-services-base-18.0.1\\res\\values-mr\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,460,579,687,828,945,1049,1142,1288,1392,1542,1662,1797,1946,2002,2064", "endColumns": "102,163,118,107,140,116,103,92,145,103,149,119,134,148,55,61,76", "endOffsets": "295,459,578,686,827,944,1048,1141,1287,1391,1541,1661,1796,1945,2001,2063,2140"}, "to": {"startLines": "65,66,67,68,69,70,71,72,74,75,76,77,78,79,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5007,5114,5282,5405,5517,5662,5783,5891,6131,6281,6389,6543,6667,6806,6959,7019,7085", "endColumns": "106,167,122,111,144,120,107,96,149,107,153,123,138,152,59,65,80", "endOffsets": "5109,5277,5400,5512,5657,5778,5886,5983,6276,6384,6538,6662,6801,6954,7014,7080,7161"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f1013ccf4ee59567949af9e71a98b092\\transformed\\exoplayer-ui-2.18.1\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,487,671,753,833,916,1003,1097,1165,1229,1319,1410,1475,1543,1603,1671,1784,1903,2014,2086,2165,2236,2306,2388,2468,2532,2595,2648,2706,2754,2815,2876,2943,3005,3071,3130,3195,3260,3325,3393,3446,3506,3580,3654", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,81,79,82,86,93,67,63,89,90,64,67,59,67,112,118,110,71,78,70,69,81,79,63,62,52,57,47,60,60,66,61,65,58,64,64,64,67,52,59,73,73,52", "endOffsets": "281,482,666,748,828,911,998,1092,1160,1224,1314,1405,1470,1538,1598,1666,1779,1898,2009,2081,2160,2231,2301,2383,2463,2527,2590,2643,2701,2749,2810,2871,2938,3000,3066,3125,3190,3255,3320,3388,3441,3501,3575,3649,3702"}, "to": {"startLines": "2,11,15,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,336,537,7612,7694,7774,7857,7944,8038,8106,8170,8260,8351,8416,8484,8544,8612,8725,8844,8955,9027,9106,9177,9247,9329,9409,9473,10212,10265,10323,10371,10432,10493,10560,10622,10688,10747,10812,10877,10942,11010,11063,11123,11197,11271", "endLines": "10,14,18,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138", "endColumns": "17,12,12,81,79,82,86,93,67,63,89,90,64,67,59,67,112,118,110,71,78,70,69,81,79,63,62,52,57,47,60,60,66,61,65,58,64,64,64,67,52,59,73,73,52", "endOffsets": "331,532,716,7689,7769,7852,7939,8033,8101,8165,8255,8346,8411,8479,8539,8607,8720,8839,8950,9022,9101,9172,9242,9324,9404,9468,9531,10260,10318,10366,10427,10488,10555,10617,10683,10742,10807,10872,10937,11005,11058,11118,11192,11266,11319"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\51b16512a656ca1125c5acd36144cb6d\\transformed\\exoplayer-core-2.18.1\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,190,259,328,403,467,564,658", "endColumns": "69,64,68,68,74,63,96,93,72", "endOffsets": "120,185,254,323,398,462,559,653,726"}, "to": {"startLines": "112,113,114,115,116,117,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "9536,9606,9671,9740,9809,9884,9948,10045,10139", "endColumns": "69,64,68,68,74,63,96,93,72", "endOffsets": "9601,9666,9735,9804,9879,9943,10040,10134,10207"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\3767e343142fab2c839a998d5bfdc1ca\\transformed\\play-services-ads-22.6.0\\res\\values-mr\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "199,245,296,352,421,493,604,682,786,837,949,1006,1116,1207,1249,1338,1374,1411,1463,1547,1588", "endColumns": "45,50,55,68,71,110,77,103,50,111,56,109,90,41,88,35,36,51,83,40,55", "endOffsets": "244,295,351,420,492,603,681,785,836,948,1005,1115,1206,1248,1337,1373,1410,1462,1546,1587,1643"}, "to": {"startLines": "199,200,201,204,205,206,207,208,209,210,211,212,213,220,221,222,223,224,225,226,251", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "16067,16117,16172,16404,16477,16553,16668,16750,16858,16913,17029,17090,17204,17770,17816,17909,17949,17990,18046,18134,20099", "endColumns": "49,54,59,72,75,114,81,107,54,115,60,113,94,45,92,39,40,55,87,44,59", "endOffsets": "16112,16167,16227,16472,16548,16663,16745,16853,16908,17024,17085,17199,17294,17811,17904,17944,17985,18041,18129,18174,20154"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\02b86cfeeba2fc323c383047854b9491\\transformed\\play-services-basement-18.2.0\\res\\values-mr\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "138", "endOffsets": "333"}, "to": {"startLines": "73", "startColumns": "4", "startOffsets": "5988", "endColumns": "142", "endOffsets": "6126"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\71743d480ebb399fb4fe6b96265e7bca\\transformed\\core-1.13.1\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,259,360,463,565,670,787", "endColumns": "99,103,100,102,101,104,116,100", "endOffsets": "150,254,355,458,560,665,782,883"}, "to": {"startLines": "52,53,54,55,56,57,58,241", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3722,3822,3926,4027,4130,4232,4337,19279", "endColumns": "99,103,100,102,101,104,116,100", "endOffsets": "3817,3921,4022,4125,4227,4332,4449,19375"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c53b383fa8484255d5fd97643f23aa97\\transformed\\foundation-release\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,140", "endColumns": "84,84", "endOffsets": "135,220"}, "to": {"startLines": "249,250", "startColumns": "4,4", "startOffsets": "19929,20014", "endColumns": "84,84", "endOffsets": "20009,20094"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\37f986109817451798d14c7952a3b6e1\\transformed\\react-android-0.76.9-debug\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,203,272,343,425,492,559,633,709,789,869,937,1020,1102,1177,1263,1350,1425,1496,1567,1658,1730,1805,1874", "endColumns": "68,78,68,70,81,66,66,73,75,79,79,67,82,81,74,85,86,74,70,70,90,71,74,68,72", "endOffsets": "119,198,267,338,420,487,554,628,704,784,864,932,1015,1097,1172,1258,1345,1420,1491,1562,1653,1725,1800,1869,1942"}, "to": {"startLines": "50,64,143,145,146,150,163,164,165,215,216,219,227,230,231,232,234,235,237,239,240,242,245,247,248", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3570,4928,11729,11863,11934,12246,13182,13249,13323,17377,17457,17702,18179,18413,18495,18570,18732,18819,18970,19117,19188,19380,19594,19787,19856", "endColumns": "68,78,68,70,81,66,66,73,75,79,79,67,82,81,74,85,86,74,70,70,90,71,74,68,72", "endOffsets": "3634,5002,11793,11929,12011,12308,13244,13318,13394,17452,17532,17765,18257,18490,18565,18651,18814,18889,19036,19183,19274,19447,19664,19851,19924"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\491ff7541ddc657f2c55a259a03f9653\\transformed\\ui-release\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,199,281,378,476,563,649,734,823,906,986,1071,1142,1218,1294,1370,1446,1512", "endColumns": "93,81,96,97,86,85,84,88,82,79,84,70,75,75,75,75,65,117", "endOffsets": "194,276,373,471,558,644,729,818,901,981,1066,1137,1213,1289,1365,1441,1507,1625"}, "to": {"startLines": "62,63,84,85,86,147,148,202,203,217,218,229,233,236,238,243,244,246", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4752,4846,7267,7364,7462,12016,12102,16232,16321,17537,17617,18342,18656,18894,19041,19452,19528,19669", "endColumns": "93,81,96,97,86,85,84,88,82,79,84,70,75,75,75,75,65,117", "endOffsets": "4841,4923,7359,7457,7544,12097,12182,16316,16399,17612,17697,18408,18727,18965,19112,19523,19589,19782"}}]}]}