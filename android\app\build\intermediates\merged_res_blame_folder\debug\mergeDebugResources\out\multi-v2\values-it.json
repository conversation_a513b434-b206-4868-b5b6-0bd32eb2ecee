{"logs": [{"outputFile": "com.gokul719.snack97152fc1f368437dac54171df4ba22bd.app-mergeDebugResources-74:/values-it/values-it.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f1013ccf4ee59567949af9e71a98b092\\transformed\\exoplayer-ui-2.18.1\\res\\values-it\\values-it.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,475,663,749,837,916,1008,1100,1178,1243,1343,1441,1506,1574,1639,1710,1838,1972,2098,2168,2261,2336,2412,2508,2606,2675,2743,2796,2854,2902,2963,3037,3108,3171,3252,3310,3371,3437,3502,3568,3620,3682,3758,3834", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,85,87,78,91,91,77,64,99,97,64,67,64,70,127,133,125,69,92,74,75,95,97,68,67,52,57,47,60,73,70,62,80,57,60,65,64,65,51,61,75,75,57", "endOffsets": "281,470,658,744,832,911,1003,1095,1173,1238,1338,1436,1501,1569,1634,1705,1833,1967,2093,2163,2256,2331,2407,2503,2601,2670,2738,2791,2849,2897,2958,3032,3103,3166,3247,3305,3366,3432,3497,3563,3615,3677,3753,3829,3887"}, "to": {"startLines": "2,11,15,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,336,525,7964,8050,8138,8217,8309,8401,8479,8544,8644,8742,8807,8875,8940,9011,9139,9273,9399,9469,9562,9637,9713,9809,9907,9976,10706,10759,10817,10865,10926,11000,11071,11134,11215,11273,11334,11400,11465,11531,11583,11645,11721,11797", "endLines": "10,14,18,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141", "endColumns": "17,12,12,85,87,78,91,91,77,64,99,97,64,67,64,70,127,133,125,69,92,74,75,95,97,68,67,52,57,47,60,73,70,62,80,57,60,65,64,65,51,61,75,75,57", "endOffsets": "331,520,708,8045,8133,8212,8304,8396,8474,8539,8639,8737,8802,8870,8935,9006,9134,9268,9394,9464,9557,9632,9708,9804,9902,9971,10039,10754,10812,10860,10921,10995,11066,11129,11210,11268,11329,11395,11460,11526,11578,11640,11716,11792,11850"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\68cda876f16b6c181c93767ccf9edd36\\transformed\\appcompat-1.7.0\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,313,422,506,611,730,808,883,975,1069,1162,1256,1357,1451,1548,1643,1735,1827,1908,2014,2121,2219,2323,2429,2536,2699,2799", "endColumns": "104,102,108,83,104,118,77,74,91,93,92,93,100,93,96,94,91,91,80,105,106,97,103,105,106,162,99,81", "endOffsets": "205,308,417,501,606,725,803,878,970,1064,1157,1251,1352,1446,1543,1638,1730,1822,1903,2009,2116,2214,2318,2424,2531,2694,2794,2876"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,236", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "881,986,1089,1198,1282,1387,1506,1584,1659,1751,1845,1938,2032,2133,2227,2324,2419,2511,2603,2684,2790,2897,2995,3099,3205,3312,3475,19326", "endColumns": "104,102,108,83,104,118,77,74,91,93,92,93,100,93,96,94,91,91,80,105,106,97,103,105,106,162,99,81", "endOffsets": "981,1084,1193,1277,1382,1501,1579,1654,1746,1840,1933,2027,2128,2222,2319,2414,2506,2598,2679,2785,2892,2990,3094,3200,3307,3470,3570,19403"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\084b6a12791f1cb8a95d16811ba661d8\\transformed\\Android-Image-Cropper-4.3.1\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12", "startColumns": "4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,179,226,284,333,411,485,557,610,667,738", "endColumns": "123,46,57,48,77,73,71,52,56,70,55", "endOffsets": "174,221,279,328,406,480,552,605,662,733,789"}, "to": {"startLines": "84,85,86,147,148,149,150,151,221,222,223", "startColumns": "4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7384,7508,7555,12332,12381,12459,12533,12605,18292,18349,18420", "endColumns": "123,46,57,48,77,73,71,52,56,70,55", "endOffsets": "7503,7550,7608,12376,12454,12528,12600,12653,18344,18415,18471"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c53b383fa8484255d5fd97643f23aa97\\transformed\\foundation-release\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,153", "endColumns": "97,98", "endOffsets": "148,247"}, "to": {"startLines": "256,257", "startColumns": "4,4", "startOffsets": "20949,21047", "endColumns": "97,98", "endOffsets": "21042,21141"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\263c8e8847d6991526302086ecc6e182\\transformed\\play-services-base-18.0.1\\res\\values-it\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,294,441,562,666,820,944,1060,1160,1313,1416,1566,1689,1841,2018,2081,2138", "endColumns": "100,146,120,103,153,123,115,99,152,102,149,122,151,176,62,56,72", "endOffsets": "293,440,561,665,819,943,1059,1159,1312,1415,1565,1688,1840,2017,2080,2137,2210"}, "to": {"startLines": "65,66,67,68,69,70,71,72,74,75,76,77,78,79,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5062,5167,5318,5443,5551,5709,5837,5957,6197,6354,6461,6615,6742,6898,7079,7146,7207", "endColumns": "104,150,124,107,157,127,119,103,156,106,153,126,155,180,66,60,76", "endOffsets": "5162,5313,5438,5546,5704,5832,5952,6056,6349,6456,6610,6737,6893,7074,7141,7202,7279"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\3767e343142fab2c839a998d5bfdc1ca\\transformed\\play-services-ads-22.6.0\\res\\values-it\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "199,241,288,352,418,490,588,667,768,818,934,992,1118,1210,1252,1351,1386,1421,1474,1556,1601", "endColumns": "41,46,63,65,71,97,78,100,49,115,57,125,91,41,98,34,34,52,81,44,55", "endOffsets": "240,287,351,417,489,587,666,767,817,933,991,1117,1209,1251,1350,1385,1420,1473,1555,1600,1656"}, "to": {"startLines": "205,206,207,210,211,212,213,214,215,216,217,218,219,228,229,230,231,232,233,234,258", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "16961,17007,17058,17314,17384,17460,17562,17645,17750,17804,17924,17986,18116,18819,18865,18968,19007,19046,19103,19189,21146", "endColumns": "45,50,67,69,75,101,82,104,53,119,61,129,95,45,102,38,38,56,85,48,59", "endOffsets": "17002,17053,17121,17379,17455,17557,17640,17745,17799,17919,17981,18111,18207,18860,18963,19002,19041,19098,19184,19233,21201"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\51b16512a656ca1125c5acd36144cb6d\\transformed\\exoplayer-core-2.18.1\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,187,259,329,405,471,558,643", "endColumns": "70,60,71,69,75,65,86,84,73", "endOffsets": "121,182,254,324,400,466,553,638,712"}, "to": {"startLines": "115,116,117,118,119,120,121,122,123", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "10044,10115,10176,10248,10318,10394,10460,10547,10632", "endColumns": "70,60,71,69,75,65,86,84,73", "endOffsets": "10110,10171,10243,10313,10389,10455,10542,10627,10701"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\d139f7b0159c383eb11b4ff91a7586c3\\transformed\\material-1.6.1\\res\\values-it\\values-it.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,223,300,399,539,622,688,783,868,930,1018,1087,1150,1223,1286,1340,1461,1518,1580,1634,1711,1848,1933,2015,2120,2201,2282,2373,2440,2506,2579,2659,2750,2825,2902,2971,3048,3136,3225,3318,3411,3485,3565,3659,3710,3776,3860,3948,4010,4074,4137,4252,4362,4468,4577", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,76,98,139,82,65,94,84,61,87,68,62,72,62,53,120,56,61,53,76,136,84,81,104,80,80,90,66,65,72,79,90,74,76,68,76,87,88,92,92,73,79,93,50,65,83,87,61,63,62,114,109,105,108,79", "endOffsets": "218,295,394,534,617,683,778,863,925,1013,1082,1145,1218,1281,1335,1456,1513,1575,1629,1706,1843,1928,2010,2115,2196,2277,2368,2435,2501,2574,2654,2745,2820,2897,2966,3043,3131,3220,3313,3406,3480,3560,3654,3705,3771,3855,3943,4005,4069,4132,4247,4357,4463,4572,4652"}, "to": {"startLines": "19,51,59,60,61,90,142,152,157,158,159,160,161,162,163,164,165,166,167,168,169,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,220", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "713,3645,4469,4568,4708,7898,11855,12658,13088,13150,13238,13307,13370,13443,13506,13560,13681,13738,13800,13854,13931,14232,14317,14399,14504,14585,14666,14757,14824,14890,14963,15043,15134,15209,15286,15355,15432,15520,15609,15702,15795,15869,15949,16043,16094,16160,16244,16332,16394,16458,16521,16636,16746,16852,18212", "endLines": "22,51,59,60,61,90,142,152,157,158,159,160,161,162,163,164,165,166,167,168,169,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,220", "endColumns": "12,76,98,139,82,65,94,84,61,87,68,62,72,62,53,120,56,61,53,76,136,84,81,104,80,80,90,66,65,72,79,90,74,76,68,76,87,88,92,92,73,79,93,50,65,83,87,61,63,62,114,109,105,108,79", "endOffsets": "876,3717,4563,4703,4786,7959,11945,12738,13145,13233,13302,13365,13438,13501,13555,13676,13733,13795,13849,13926,14063,14312,14394,14499,14580,14661,14752,14819,14885,14958,15038,15129,15204,15281,15350,15427,15515,15604,15697,15790,15864,15944,16038,16089,16155,16239,16327,16389,16453,16516,16631,16741,16847,16956,18287"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\491ff7541ddc657f2c55a259a03f9653\\transformed\\ui-release\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,205,292,390,490,577,656,762,855,950,1034,1122,1207,1283,1355,1425,1503,1572", "endColumns": "99,86,97,99,86,78,105,92,94,83,87,84,75,71,69,77,68,120", "endOffsets": "200,287,385,485,572,651,757,850,945,1029,1117,1202,1278,1350,1420,1498,1567,1688"}, "to": {"startLines": "62,63,87,88,89,155,156,208,209,226,227,237,241,244,246,251,252,254", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4791,4891,7613,7711,7811,12903,12982,17126,17219,18647,18731,19408,19736,19966,20108,20513,20591,20741", "endColumns": "99,86,97,99,86,78,105,92,94,83,87,84,75,71,69,77,68,120", "endOffsets": "4886,4973,7706,7806,7893,12977,13083,17214,17309,18726,18814,19488,19807,20033,20173,20586,20655,20857"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\268d4161c8b8edb1216e3f661bb7ee02\\transformed\\browser-1.6.0\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,253,363", "endColumns": "99,97,109,102", "endOffsets": "150,248,358,461"}, "to": {"startLines": "83,143,144,145", "startColumns": "4,4,4,4", "startOffsets": "7284,11950,12048,12158", "endColumns": "99,97,109,102", "endOffsets": "7379,12043,12153,12256"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\02b86cfeeba2fc323c383047854b9491\\transformed\\play-services-basement-18.2.0\\res\\values-it\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "131", "endOffsets": "326"}, "to": {"startLines": "73", "startColumns": "4", "startOffsets": "6061", "endColumns": "135", "endOffsets": "6192"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\37f986109817451798d14c7952a3b6e1\\transformed\\react-android-0.76.9-debug\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,209,280,352,440,520,604,694,775,863,949,1026,1106,1185,1260,1330,1399,1489,1564,1645", "endColumns": "69,83,70,71,87,79,83,89,80,87,85,76,79,78,74,69,68,89,74,80,86", "endOffsets": "120,204,275,347,435,515,599,689,770,858,944,1021,1101,1180,1255,1325,1394,1484,1559,1640,1727"}, "to": {"startLines": "50,64,146,153,154,170,171,224,225,235,238,239,240,242,243,245,247,248,250,253,255", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3575,4978,12261,12743,12815,14068,14148,18476,18566,19238,19493,19579,19656,19812,19891,20038,20178,20247,20438,20660,20862", "endColumns": "69,83,70,71,87,79,83,89,80,87,85,76,79,78,74,69,68,89,74,80,86", "endOffsets": "3640,5057,12327,12810,12898,14143,14227,18561,18642,19321,19574,19651,19731,19886,19961,20103,20242,20332,20508,20736,20944"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\71743d480ebb399fb4fe6b96265e7bca\\transformed\\core-1.13.1\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,354,456,565,672,802", "endColumns": "97,101,98,101,108,106,129,100", "endOffsets": "148,250,349,451,560,667,797,898"}, "to": {"startLines": "52,53,54,55,56,57,58,249", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3722,3820,3922,4021,4123,4232,4339,20337", "endColumns": "97,101,98,101,108,106,129,100", "endOffsets": "3815,3917,4016,4118,4227,4334,4464,20433"}}]}]}