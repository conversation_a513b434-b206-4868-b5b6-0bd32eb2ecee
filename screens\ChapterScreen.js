import React, { useState, useEffect, useContext, useCallback, useMemo } from 'react';
import { View, Text, StyleSheet, FlatList, SectionList, Image, TouchableOpacity, ActivityIndicator, Alert, ScrollView } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useNavigation, useRoute } from '@react-navigation/native';
import { MotiView } from 'moti';
import { Ionicons } from '@expo/vector-icons';
import { Asset } from 'expo-asset';
import * as Sharing from 'expo-sharing';
import * as FileSystem from 'expo-file-system';
import { ProgressBar, Colors } from 'react-native-paper';
// Firebase Storage imports removed - using test PDFs for now
import { ThemeContext } from './ThemeContext';
import PdfService from '../src/services/PdfService';

const ChapterScreen = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const { category } = route.params;

  const { darkMode } = useContext(ThemeContext);
  const [bookmarked, setBookmarked] = useState({});
  const [isLoading, setIsLoading] = useState(null);
  const [downloading, setDownloading] = useState({});
  const [downloadProgress, setDownloadProgress] = useState({});
  const [downloadedUris, setDownloadedUris] = useState({});
  const [viewMode, setViewMode] = useState('grid'); // Add state for view mode ('grid' or 'list')

  useEffect(() => {
    // Load bookmarks
    AsyncStorage.getItem('bookmarks').then((data) => {
      if (data) setBookmarked(JSON.parse(data));
    });
    // Load previously downloaded URIs on mount
    AsyncStorage.getItem('downloadedPdfs').then((data) => {
      if (data) {
        console.log("Loaded downloaded URIs from storage:", data);
        setDownloadedUris(JSON.parse(data));
      }
    });
  }, []);

  const toggleBookmark = useCallback((id) => {
    setBookmarked((prev) => {
      const updated = { ...prev, [id]: !prev[id] };
      AsyncStorage.setItem('bookmarks', JSON.stringify(updated));
      return updated;
    });
  }, []); // Added useCallback with empty dependency array as it doesn't depend on component state/props

  // --- PDF Logic (Local Asset or Firebase) ---
  const handlePdfPress = useCallback(async (item) => {
    // Log the item being processed
    console.log(`handlePdfPress called for item: ID=${item.id}, Title=${item.title}`);

    // Check if already downloaded (Firebase only, local assets are handled differently)
    const existingLocalUri = downloadedUris[item.id];
    if (existingLocalUri && item.pdfUrl && !item.pdfAsset) { // Check if URI exists and it's not a local asset item
      console.log(`PDF already downloaded for ${item.title}, navigating to viewer with URI: ${existingLocalUri}`);
      navigation.navigate('PdfViewer', { sourceUri: existingLocalUri });
      return; // Exit early
    }

    if (isLoading === item.id || downloading[item.id]) return; // Prevent multiple clicks while loading/downloading

    // 1. Handle Local Assets (Bundled with app)
    if (item.pdfAsset) {
      console.log(`Handling local asset for: ${item.title}`);
      setIsLoading(item.id); // Use general loading state for preparing asset
      try {
        const asset = Asset.fromModule(item.pdfAsset);

        // Use asset.uri which might be a content:// or other internal URI
        if (!asset.uri) {
            throw new Error('Asset URI is missing.');
        }
        console.log(`Asset URI found: ${asset.uri}`);

        // Define a temporary path in the cache directory
        const filename = asset.name || `${item.id}_local.pdf`; // Use asset name or generate one
        const tempUri = `${FileSystem.cacheDirectory}${filename}`;
        console.log(`Attempting to download asset from ${asset.uri} to ${tempUri}`);

        // Try downloading the asset using FileSystem
        await FileSystem.downloadAsync(asset.uri, tempUri);
        console.log(`Asset downloaded via FileSystem to: ${tempUri}`);

        // Share the downloaded temporary file
        await Sharing.shareAsync(tempUri, { mimeType: 'application/pdf', dialogTitle: `Open ${item.title} with...` });
        console.log('Sharing dialog prompted for downloaded local asset.');

      } catch (error) {
        console.error(`Error preparing/sharing local PDF for ${item.title}:`, error);
        Alert.alert('File Error', `Could not open the local file for "${item.title}". Please try again.`);
      } finally {
        setIsLoading(null); // Reset loading state
      }
      return; // Exit after handling local asset
    }

    // 2. Handle Firebase Storage PDFs if no local asset (Download First Approach)
    console.log(`Handling Firebase PDF for: ${item.title}`);
    setIsLoading(item.id); // Indicate initial loading (fetching URL)

    // Use the item's pdfUrl if available, otherwise fall back to test PDFs
    let downloadUrl = item.pdfUrl;

    if (!downloadUrl) {
        // ** TEMPORARY: Use test PDFs for immediate testing **
        // TODO: Replace with actual Firebase Storage URLs after uploading PDFs
        if (item.id.startsWith('xi-')) {
            // Use a sample PDF for testing (Advanced Notes XI)
            downloadUrl = 'https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf';
        } else if (item.id.startsWith('xii-')) {
            // Use a sample PDF for testing (Advanced Notes XII - Chapters)
            downloadUrl = 'https://www.africau.edu/images/default/sample.pdf';
        } else if (item.id === '1') {
            // First book (Physics/Chemistry/Biology Class 12 Part 1 or Biology Class 12) - Use sample PDF
            downloadUrl = 'https://www.africau.edu/images/default/sample.pdf';
        } else if (item.id === '2') {
            // Second book (Physics/Chemistry Class 12 Part 2 or Biology Class 11) - Use sample PDF
            downloadUrl = 'https://www.africau.edu/images/default/sample.pdf';
        } else if (item.id === '3') {
            // Third book (Physics/Chemistry Class 11 Part 1) - Use sample PDF
            downloadUrl = 'https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf';
        } else if (item.id === '4') {
            // Fourth book (Physics/Chemistry Class 11 Part 2) - Use sample PDF
            downloadUrl = 'https://www.africau.edu/images/default/sample.pdf';
        } else {
            console.error("Configuration Error: Item does not have pdfAsset and is not explicitly handled for Firebase path:", item.id);
            Alert.alert('Configuration Error', `Could not find the correct path for "${item.title}". Item ID: ${item.id}`);
            setIsLoading(null);
            return;
        }
    }
    // ***************************

    console.log(`Using test PDF URL: ${downloadUrl}`);

    // --- Start Download ---
    setDownloading(prev => ({ ...prev, [item.id]: true }));
    setDownloadProgress(prev => ({ ...prev, [item.id]: 0 }));

    const safeFilename = item.id.replace(/[^a-zA-Z0-9]/g, '_');
    const localFilename = `${safeFilename}.pdf`; // Use a consistent filename
    const localUri = FileSystem.documentDirectory + localFilename;

    console.log(`Starting download from ${downloadUrl} to ${localUri}`);

    const downloadResumable = FileSystem.createDownloadResumable(
      downloadUrl,
      localUri,
      {},
      (downloadProgressData) => {
        const progress = downloadProgressData.totalBytesWritten / downloadProgressData.totalBytesExpected;
        setDownloadProgress(prev => ({ ...prev, [item.id]: progress }));
      }
    );

    try {
      const { uri } = await downloadResumable.downloadAsync();
      console.log('Finished downloading to ', uri);
      setDownloading(prev => ({ ...prev, [item.id]: false })); // Download finished

      // Store the local URI for offline access and deletion
      const updatedUris = { ...downloadedUris, [item.id]: uri };
      setDownloadedUris(updatedUris);
      await AsyncStorage.setItem('downloadedPdfs', JSON.stringify(updatedUris));
      console.log(`Saved URI for ${item.id}: ${uri}`);

      // Navigate to the PdfViewerScreen with the *local* URI of the downloaded file
      navigation.navigate('PdfViewer', { sourceUri: uri });

    } catch (e) {
      console.error('Download error:', e);
      Alert.alert('Download Failed', `Could not download "${item.title}". Please check your internet connection and storage space, then try again.`);
      setDownloading(prev => ({ ...prev, [item.id]: false })); // Reset download state on error
      setDownloadProgress(prev => ({ ...prev, [item.id]: 0 })); // Reset progress
    }
  }, [downloadedUris, isLoading, downloading, navigation]); // Dependencies for handlePdfPress

  // --- Delete Logic ---
  const handleDeletePress = useCallback(async (item) => {
    const localUriToDelete = downloadedUris[item.id];
    if (!localUriToDelete) {
      console.error(`Delete Error: No local URI found in state for item ID: ${item.id}`);
      Alert.alert('Deletion Error', `Could not find the record of the downloaded file for "${item.title}".`);
      return;
    }

    console.log(`Attempting to delete file at: ${localUriToDelete}`);
    try {
      const fileInfo = await FileSystem.getInfoAsync(localUriToDelete);
      if (!fileInfo.exists) {
          console.log(`File ${localUriToDelete} does not exist, removing from state.`);
      } else {
          await FileSystem.deleteAsync(localUriToDelete, { idempotent: true });
          console.log(`Successfully deleted file: ${localUriToDelete}`);
      }

      const updatedUris = { ...downloadedUris };
      delete updatedUris[item.id];
      setDownloadedUris(updatedUris);
      await AsyncStorage.setItem('downloadedPdfs', JSON.stringify(updatedUris));
      Alert.alert('Deleted', `${item.title} has been removed from your device.`);

    } catch (error) {
      console.error(`Error deleting file ${localUriToDelete}:`, error);
      Alert.alert('Deletion Error', `Could not delete the downloaded file for "${item.title}". Please try again.`);
     }
   }, [downloadedUris]); // Dependencies for handleDeletePress

   // Helper function to get the correct PDF URL for Chemistry Chapter 12
   const getChemistryChapter12PdfUrl = (chapterNumber) => {
    return PdfService.getPdfUrl('chemistry', 'chapters12', chapterNumber);
  };

  // Helper function to get the correct PDF URL for Biology Chapter 12
  const getBiologyChapter12PdfUrl = (chapterNumber) => {
    return PdfService.getPdfUrl('biology', 'chapters12', chapterNumber);
  };

    // Helper function to get the correct PDF URL for Physics Chapter 12
  const getPhysicsChapter12PdfUrl = (chapterNumber) => {
    return PdfService.getPdfUrl('physics', 'chapters12', chapterNumber);
  };

  // Define book/chapter data. Add pdfAsset back for local files.
  // Using useMemo to prevent redefining these arrays on every render
  const books = useMemo(() => {
    let bookImage;
    if (category === 'Physics') {
      bookImage = require('../assets/images/physicsbook.png');
    } else if (category === 'Chemistry') {
      bookImage = require('../assets/images/chemistry_book.png');
    } else if (category === 'Biology') {
      bookImage = require('../assets/images/biology_book.png');
    } else {
      bookImage = require('../assets/images/physicsbook.png'); // Default to physics book if category is unknown
    }

    let bookTitles = [];
    if (category === 'Physics') {
      bookTitles = [
        { id: '1', title: 'Physics English Book XII Part-1', image: bookImage, pdfUrl: PdfService.getPdfUrl('physics', 'class12Part1') },
        { id: '2', title: 'Physics English Book XII Part-2', image: bookImage, pdfUrl: PdfService.getPdfUrl('physics', 'class12Part2') },
        { id: '3', title: 'Physics English Book XI Part-1', image: bookImage, pdfUrl: PdfService.getPdfUrl('physics', 'class11Part1') },
        { id: '4', title: 'Physics English Book XI Part-2', image: bookImage, pdfUrl: PdfService.getPdfUrl('physics', 'class11Part2') },
      ];
    } else if (category === 'Chemistry') {
      bookTitles = [
        { id: '1', title: 'Chemistry English Book XII Part-1', image: bookImage, pdfUrl: PdfService.getPdfUrl('chemistry', 'class12Part1') },
        { id: '2', title: 'Chemistry English Book XII Part-2', image: bookImage, pdfUrl: PdfService.getPdfUrl('chemistry', 'class12Part2') },
        { id: '3', title: 'Chemistry English Book XI Part-1', image: bookImage, pdfUrl: PdfService.getPdfUrl('chemistry', 'class11Part1') },
        { id: '4', title: 'Chemistry English Book XI Part-2', image: bookImage, pdfUrl: PdfService.getPdfUrl('chemistry', 'class11Part2') },
      ];
    } else if (category === 'Biology') {
      bookTitles = [
        { id: '1', title: 'Biology English Book XII', image: bookImage, pdfUrl: PdfService.getPdfUrl('biology', 'class12') },
        { id: '2', title: 'Biology English Book XI', image: bookImage, pdfUrl: PdfService.getPdfUrl('biology', 'class11') },
      ];
    } else {
      bookTitles =  [
        { id: '1', title: 'Physics English Book XII Part-1', image: bookImage, pdfUrl: PdfService.getPdfUrl('physics', 'class12Part1') },
        { id: '2', title: 'Physics English Book XII Part-2', image: bookImage, pdfUrl: PdfService.getPdfUrl('physics', 'class12Part2') },
        { id: '3', title: 'Physics English Book XI Part-1', image: bookImage, pdfUrl: PdfService.getPdfUrl('physics', 'class11Part1') },
        { id: '4', title: 'Physics English Book XI Part-2', image: bookImage, pdfUrl: PdfService.getPdfUrl('physics', 'class11Part2') },
      ];
    }
    return bookTitles;
  }, [category]);

  const advancedNotesXII = useMemo(() => {
    let chapterImage;
    if (category === 'Physics') {
      chapterImage = require('../assets/images/physicsbook.png');
    } else if (category === 'Chemistry') {
      chapterImage = require('../assets/images/chemistry_book.png');
    } else if (category === 'Biology') {
      chapterImage = require('../assets/images/biology_book.png');
    } else {
      chapterImage = require('../assets/images/physicsbook.png'); // Default to physics book if category is unknown
    }

    return Array.from({ length: 14 }, (_, i) => ({
      id: `xii-${i + 1}`,
      title: `Chapter ${i + 1}`,
      image: chapterImage,
      pdfUrl: category === 'Physics' ? getPhysicsChapter12PdfUrl(i + 1) :
              category === 'Chemistry' ? getChemistryChapter12PdfUrl(i + 1) :
              category === 'Biology' ? getBiologyChapter12PdfUrl(i + 1) :
              null
    }));
  }, [category]);

  const advancedNotesXI = useMemo(() => {
    let chapterImage;
    if (category === 'Physics') {
      chapterImage = require('../assets/images/physicsbook.png');
    } else if (category === 'Chemistry') {
      chapterImage = require('../assets/images/chemistry_book.png');
    } else if (category === 'Biology') {
      chapterImage = require('../assets/images/biology_book.png');
    } else {
      chapterImage = require('../assets/images/physicsbook.png');
    }

    return Array.from({ length: 14 }, (_, i) => ({
      id: `xi-${i + 1}`,
      title: `Chapter ${i + 1}`,
      image: chapterImage,
    }));
  }, [category]);


  // Renderer for Grid View Items
  const renderBookItem = useCallback(({ item, index }) => {
    const isCurrentlyLoading = isLoading === item.id;
    const isCurrentlyDownloading = downloading[item.id];
    const progress = downloadProgress[item.id] || 0;
    const isDownloaded = !!downloadedUris[item.id] && item.pdfUrl && !item.pdfAsset;
    const bookmarkedStyle = bookmarked[item.id] ? styles.bookmarkedItem : {};

    let buttonText = 'Open';
    if (item.pdfUrl && !item.pdfAsset) {
        if (isDownloaded) buttonText = 'Open';
        else buttonText = 'Download';
    }

    const isDisabled = isCurrentlyLoading || isCurrentlyDownloading;
    const labelBase = item.title;
    let buttonLabel = `Open ${labelBase}`;
    let buttonHint = `Opens ${labelBase}`;
    if (item.pdfUrl && !item.pdfAsset) {
        if (isDownloaded) {
            buttonLabel = `Open downloaded ${labelBase}`;
            buttonHint = `Opens the downloaded file for ${labelBase}`;
        } else {
            buttonLabel = `Download ${labelBase}`;
            buttonHint = `Downloads the file for ${labelBase}`;
        }
    }
    if (isCurrentlyDownloading) {
        buttonLabel = `Downloading ${labelBase}, ${Math.round(progress * 100)}% complete`;
        buttonHint = `Download in progress`;
    } else if (isCurrentlyLoading) {
        buttonLabel = `Loading ${labelBase}`;
        buttonHint = `Please wait`;
    }

    return (
      <MotiView
        style={[styles.bookItemContainer, bookmarkedStyle]}
        from={{ opacity: 0, translateY: 20 }}
        animate={{ opacity: 1, translateY: 0 }}
        transition={{ delay: index * 50 }}
      >
        <TouchableOpacity
          onLongPress={() => toggleBookmark(item.id)}
          accessibilityRole="button"
          accessibilityLabel={`${labelBase}${bookmarked[item.id] ? ', Bookmarked' : ''}`}
          accessibilityHint={`Long press to ${bookmarked[item.id] ? 'remove bookmark' : 'add bookmark'}`}
         >
          <Image source={item.image} style={styles.bookImage} resizeMode="cover" />
          <View style={styles.titleContainer}>
            <Text style={styles.bookTitle}>{item.title}</Text>
            {isDownloaded && (
              <Ionicons name="checkmark-circle" size={16} color="green" style={styles.checkmarkIcon} />
            )}
          </View>
        </TouchableOpacity>
        <TouchableOpacity
          style={styles.downloadButton}
          onPress={() => handlePdfPress(item)}
          disabled={isDisabled}
          accessibilityLabel={buttonLabel}
          accessibilityHint={buttonHint}
          accessibilityRole="button"
          accessibilityState={{ disabled: isDisabled }}
        >
          {isCurrentlyLoading ? (
            <ActivityIndicator size="small" color="#FFFFFF" accessibilityLabel={`Loading ${labelBase}`} />
          ) : isCurrentlyDownloading ? (
            <View style={styles.progressContainer}>
             <ProgressBar progress={progress} color={Colors.white} style={styles.progressBar} />
             <Text style={styles.progressText}>{`${Math.round(progress * 100)}%`}</Text>
            </View>
          ) : (
            <Text style={styles.downloadButtonText}>{buttonText}</Text>
          )}
        </TouchableOpacity>
        {isDownloaded && (
          <TouchableOpacity
            style={styles.deleteButton}
            onPress={() => handleDeletePress(item)}
            disabled={isDisabled}
            accessibilityLabel={`Delete downloaded ${labelBase}`}
            accessibilityHint={`Deletes the downloaded file for ${labelBase} from your device`}
            accessibilityRole="button"
            accessibilityState={{ disabled: isDisabled }}
          >
            <Ionicons name="trash-outline" size={18} color="#FFFFFF" />
          </TouchableOpacity>
        )}
      </MotiView>
    );
  }, [isLoading, downloading, downloadProgress, downloadedUris, bookmarked, toggleBookmark, handlePdfPress, handleDeletePress]); // Dependencies for renderBookItem

  // Renderer for List View Items
  const renderListItem = useCallback(({ item, index }) => {
    const isCurrentlyLoading = isLoading === item.id;
    const isCurrentlyDownloading = downloading[item.id];
    const progress = downloadProgress[item.id] || 0;
    const isDownloaded = !!downloadedUris[item.id] && item.pdfUrl && !item.pdfAsset;
    const bookmarkedStyle = bookmarked[item.id] ? styles.bookmarkedItem : {};

    let buttonText = 'Open';
    if (item.pdfUrl && !item.pdfAsset) {
        if (isDownloaded) buttonText = 'Open';
        else buttonText = 'Download';
    }

    const isDisabled = isCurrentlyLoading || isCurrentlyDownloading;
    const labelBase = item.title;
    let buttonLabel = `Open ${labelBase}`;
    let buttonHint = `Opens ${labelBase}`;
    if (item.pdfUrl && !item.pdfAsset) {
        if (isDownloaded) {
            buttonLabel = `Open downloaded ${labelBase}`;
            buttonHint = `Opens the downloaded file for ${labelBase}`;
        } else {
            buttonLabel = `Download ${labelBase}`;
            buttonHint = `Downloads the file for ${labelBase}`;
        }
    }
     if (isCurrentlyDownloading) {
        buttonLabel = `Downloading ${labelBase}, ${Math.round(progress * 100)}% complete`;
        buttonHint = `Download in progress`;
    } else if (isCurrentlyLoading) {
        buttonLabel = `Loading ${labelBase}`;
        buttonHint = `Please wait`;
    }

    return (
      <MotiView
        style={[styles.listItemContainer, bookmarkedStyle]} // Apply bookmark style if needed
        from={{ opacity: 0, translateX: -20 }}
        animate={{ opacity: 1, translateX: 0 }}
        transition={{ delay: index * 50 }}
      >
        <Image source={item.image} style={styles.listItemImage} resizeMode="cover" />
        <View style={styles.listItemTextContainer}>
          <TouchableOpacity onLongPress={() => toggleBookmark(item.id)} style={{ flexShrink: 1 }}>
             <View style={styles.titleContainer}>
                <Text style={styles.listItemTitle}>{item.title}</Text>
                 {isDownloaded && (
                   <Ionicons name="checkmark-circle" size={16} color="green" style={styles.checkmarkIcon} />
                 )}
             </View>
          </TouchableOpacity>
          {/* Optional: Add description or other info here later */}
        </View>
        <View style={styles.listItemButtonContainer}>
          <TouchableOpacity
            style={[styles.downloadButton, styles.listItemButton]}
            onPress={() => handlePdfPress(item)}
            disabled={isDisabled}
            accessibilityLabel={buttonLabel}
            accessibilityHint={buttonHint}
            accessibilityRole="button"
            accessibilityState={{ disabled: isDisabled }}
          >
            {isCurrentlyLoading ? (
              <ActivityIndicator size="small" color="#FFFFFF" accessibilityLabel={`Loading ${labelBase}`} />
            ) : isCurrentlyDownloading ? (
              <View style={[styles.progressContainer, {width: '100%'}]}> {/* Adjust progress width for list */}
                <ProgressBar progress={progress} color={Colors.white} style={styles.progressBar} />
                <Text style={styles.progressText}>{`${Math.round(progress * 100)}%`}</Text>
              </View>
            ) : (
              <Text style={styles.downloadButtonText}>{buttonText}</Text>
            )}
          </TouchableOpacity>
          {isDownloaded && (
            <TouchableOpacity
              style={[styles.deleteButton, styles.listItemDeleteButton]}
              onPress={() => handleDeletePress(item)}
              disabled={isDisabled}
              accessibilityLabel={`Delete downloaded ${labelBase}`}
              accessibilityHint={`Deletes the downloaded file for ${labelBase} from your device`}
              accessibilityRole="button"
              accessibilityState={{ disabled: isDisabled }}
            >
              <Ionicons name="trash-outline" size={18} color="#FFFFFF" />
            </TouchableOpacity>
          )}
        </View>
      </MotiView>
    );
  }, [isLoading, downloading, downloadProgress, downloadedUris, bookmarked, toggleBookmark, handlePdfPress, handleDeletePress]); // Dependencies for renderListItem

  // Prepare data for SectionList (memoized)
  const sections = useMemo(() => {
    const bookmarkedItems = [...books, ...advancedNotesXII, ...advancedNotesXI].filter(item => bookmarked[item.id]);
    const sectionsData = [
      { title: 'Books', data: books },
      { title: 'Advanced Notes (XII)', data: advancedNotesXII },
      { title: 'Advanced Notes (XI)', data: advancedNotesXI },
    ];
    if (bookmarkedItems.length > 0) {
      sectionsData.push({ title: 'Bookmarked', data: bookmarkedItems });
    }
    // Filter out sections with no data
    return sectionsData.filter(section => section.data.length > 0);
  }, [books, advancedNotesXII, advancedNotesXI, bookmarked]); // Dependencies for section data


  // Renderer for Section Headers
  const renderSectionHeader = ({ section: { title } }) => (
    <Text style={styles.subTitle}>{title}</Text>
  );


  const ListHeader = () => (
    <View style={styles.headerContainer}>
      {/* Title and View Mode Toggle */}
      <View style={styles.titleRow}>
        <Text style={styles.title}>{category}</Text>
        <View style={styles.viewModeToggleContainer}>
          <TouchableOpacity onPress={() => setViewMode('grid')} style={viewMode === 'grid' ? styles.viewModeButtonActive : styles.viewModeButton}>
            <Ionicons name="apps-outline" size={24} color={viewMode === 'grid' ? '#007AFF' : (darkMode ? '#aaa' : '#555')} />
          </TouchableOpacity>
          <TouchableOpacity onPress={() => setViewMode('list')} style={viewMode === 'list' ? styles.viewModeButtonActive : styles.viewModeButton}>
            <Ionicons name="list-outline" size={28} color={viewMode === 'list' ? '#007AFF' : (darkMode ? '#aaa' : '#555')} />
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );

  // Main component render
  return (
    <View style={styles.container}>
      <ListHeader />
      {category === 'Math' ? (
        <View style={styles.comingSoonContainer}>
          <Text style={styles.comingSoonText}>Coming Soon</Text>
        </View>
      ) : viewMode === 'grid' ? (
        // Render horizontal FlatLists for grid view
        // Note: These are not inside the main list's header anymore
        // We wrap these in a ScrollView in case content overflows vertically
        <ScrollView showsVerticalScrollIndicator={false}>
          <Text style={styles.subTitle}>Books</Text>
          <FlatList
            data={books}
            horizontal
            showsHorizontalScrollIndicator={false}
            keyExtractor={(item) => item.id}
            renderItem={({ item, index }) => renderBookItem({ item, index })}
            contentContainerStyle={styles.bookList}
            initialNumToRender={5}
            windowSize={7}
            maxToRenderPerBatch={5}
          />

          <Text style={styles.subTitle}>Advanced Notes (XII)</Text>
          <FlatList
            data={advancedNotesXII}
            horizontal
            showsHorizontalScrollIndicator={false}
            keyExtractor={(item) => item.id}
            renderItem={({ item, index }) => renderBookItem({ item, index })}
            contentContainerStyle={styles.bookList}
            initialNumToRender={5}
            windowSize={7}
            maxToRenderPerBatch={5}
          />

          <Text style={styles.subTitle}>Advanced Notes (XI)</Text>
          <FlatList
            data={advancedNotesXI}
            horizontal
            showsHorizontalScrollIndicator={false}
            keyExtractor={(item) => item.id}
            renderItem={({ item, index }) => renderBookItem({ item, index })}
            contentContainerStyle={styles.bookList}
            initialNumToRender={5}
            windowSize={7}
            maxToRenderPerBatch={5}
          />

          {sections.find(s => s.title === 'Bookmarked') && (
             <View style={styles.bookmarkSection}>
               <Text style={styles.subTitle}>Bookmarked</Text>
               <FlatList
                 data={sections.find(s => s.title === 'Bookmarked').data}
                 horizontal
                 showsHorizontalScrollIndicator={false}
                 keyExtractor={(item) => item.id + '-fav'}
                 renderItem={({ item, index }) => renderBookItem({ item, index })}
                 contentContainerStyle={styles.bookList}
                 initialNumToRender={5}
                 windowSize={7}
                 maxToRenderPerBatch={5}
               />
             </View>
           )}
        </ScrollView> // End ScrollView for grid content
      ) : (
        // Render SectionList for list view
        <SectionList
          sections={sections}
          keyExtractor={(item, index) => item.id + index}
          renderItem={renderListItem} // Use the new list item renderer
          renderSectionHeader={renderSectionHeader}
          stickySectionHeadersEnabled={false}
          contentContainerStyle={{ paddingBottom: 50 }} // Add padding at the bottom
          // Add performance props similar to FlatList if needed
          initialNumToRender={10}
          windowSize={15}
          maxToRenderPerBatch={10}
        />
      )}
    </View>
  );
};

// Styles
const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 15,
    paddingBottom: 20,
  },
  headerContainer: {
    paddingBottom: 10,
    paddingHorizontal: 5,
  },
  titleRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 28,
    fontFamily: 'Poppins_700Bold',
    flexShrink: 1,
  },
  viewModeToggleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  viewModeButton: {
    padding: 5,
    marginLeft: 8,
  },
  viewModeButtonActive: {
    padding: 5,
    marginLeft: 8,
    backgroundColor: 'rgba(0, 122, 255, 0.1)',
    borderRadius: 5,
  },
  subTitle: {
    fontSize: 18,
    fontFamily: 'Poppins_600SemiBold',
    marginTop: 20,
    marginBottom: 10,
    paddingHorizontal: 5,
  },
  comingSoonContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingBottom: 50,
  },
  comingSoonText: {
    fontSize: 22,
    fontFamily: 'Poppins_600SemiBold',
    color: '#888',
  },
  // Grid Styles
  bookList: {
    gap: 12,
    paddingHorizontal: 5,
    paddingBottom: 10,
  },
  bookItemContainer: {
    width: 120,
    alignItems: 'center',
    padding: 10,
    backgroundColor: '#f5f5f5',
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    marginBottom: 10,
  },
  bookImage: {
    width: 100,
    height: 130,
    borderRadius: 8,
    marginBottom: 8,
  },
  bookTitle: {
    fontSize: 14,
    fontFamily: 'Poppins_500Medium',
    textAlign: 'center',
    flexShrink: 1,
  },
  // List View Styles
  listItemContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 5,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
    backgroundColor: '#fff',
  },
  listItemImage: {
    width: 50,
    height: 70,
    borderRadius: 4,
    marginRight: 15,
  },
  listItemTextContainer: {
    flex: 1,
    justifyContent: 'center',
    marginRight: 10,
  },
  listItemTitle: {
    fontSize: 16,
    fontFamily: 'Poppins_500Medium',
  },
  listItemButtonContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  listItemButton: {
    paddingVertical: 6,
    paddingHorizontal: 12,
    backgroundColor: '#007AFF',
    borderRadius: 6,
  },
  listItemDeleteButton: {
    marginLeft: 8,
    backgroundColor: '#FF3B30',
    paddingVertical: 6,
    paddingHorizontal: 10,
    borderRadius: 6,
  },
  downloadButton: {
    backgroundColor: '#007AFF',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 8,
    marginTop: 8,
  },
  downloadButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontFamily: 'Poppins_600SemiBold',
    textAlign: 'center',
  },
  deleteButton: {
    backgroundColor: '#FF3B30',
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 8,
    marginTop: 8,
    marginLeft: 8,
  },
  progressContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  progressBar: {
    flex: 1,
    height: 8,
    borderRadius: 4,
  },
  progressText: {
    marginLeft: 8,
    color: '#FFFFFF',
    fontSize: 12,
    fontFamily: 'Poppins_500Medium',
  },
  bookmarkedItem: {
    borderColor: '#007AFF',
    borderWidth: 2,
  },
  checkmarkIcon: {
    marginLeft: 6,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  bookmarkSection: {
    marginTop: 20,
  },
});

export default ChapterScreen;