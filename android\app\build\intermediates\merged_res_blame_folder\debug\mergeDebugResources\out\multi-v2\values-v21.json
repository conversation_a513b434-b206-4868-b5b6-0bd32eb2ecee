{"logs": [{"outputFile": "com.gokul719.snack97152fc1f368437dac54171df4ba22bd.app-mergeDebugResources-74:/values-v21/values-v21.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\32330779d42f886d84c661101fbf7e17\\transformed\\media-1.4.3\\res\\values-v21\\values-v21.xml", "from": {"startLines": "2,5,8,11", "startColumns": "4,4,4,4", "startOffsets": "55,223,386,554", "endLines": "4,7,10,13", "endColumns": "12,12,12,12", "endOffsets": "218,381,549,716"}, "to": {"startLines": "344,347,351,355", "startColumns": "4,4,4,4", "startOffsets": "25243,25411,25700,25996", "endLines": "346,349,353,357", "endColumns": "12,12,12,12", "endOffsets": "25406,25569,25863,26158"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\68cda876f16b6c181c93767ccf9edd36\\transformed\\appcompat-1.7.0\\res\\values-v21\\values-v21.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,17,19,20,21,22,24,26,27,28,29,30,32,34,36,38,40,42,43,48,50,52,53,54,56,58,59,60,61,62,63,106,109,152,155,158,160,162,164,167,171,174,175,176,179,180,181,182,183,184,187,188,190,192,194,196,200,202,203,204,205,207,211,213,215,216,217,218,219,220,222,223,224,234,235,236,248", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,146,249,352,457,564,673,782,891,1000,1109,1216,1319,1438,1593,1748,1853,1974,2075,2222,2363,2466,2585,2692,2795,2950,3121,3270,3435,3592,3743,3862,4213,4362,4511,4623,4770,4923,5070,5145,5234,5321,5422,5525,8283,8468,11238,11435,11634,11757,11880,11993,12176,12431,12632,12721,12832,13065,13166,13261,13384,13513,13630,13807,13906,14041,14184,14319,14438,14639,14758,14851,14962,15018,15125,15320,15431,15564,15659,15750,15841,15934,16051,16190,16261,16344,16967,17024,17082,17706", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,16,18,19,20,21,23,25,26,27,28,29,31,33,35,37,39,41,42,47,49,51,52,53,55,57,58,59,60,61,62,105,108,151,154,157,159,161,163,166,170,173,174,175,178,179,180,181,182,183,186,187,189,191,193,195,199,201,202,203,204,206,210,212,214,215,216,217,218,219,221,222,223,233,234,235,247,259", "endColumns": "90,102,102,104,106,108,108,108,108,108,106,102,118,12,12,104,120,100,12,12,102,118,106,102,12,12,12,12,12,12,118,12,12,12,111,146,12,12,74,88,86,100,102,12,12,12,12,12,12,12,12,12,12,12,88,110,12,100,94,122,128,116,12,98,12,12,12,12,12,12,92,110,55,12,12,12,12,94,90,90,92,116,12,70,82,12,56,57,12,12", "endOffsets": "141,244,347,452,559,668,777,886,995,1104,1211,1314,1433,1588,1743,1848,1969,2070,2217,2358,2461,2580,2687,2790,2945,3116,3265,3430,3587,3738,3857,4208,4357,4506,4618,4765,4918,5065,5140,5229,5316,5417,5520,8278,8463,11233,11430,11629,11752,11875,11988,12171,12426,12627,12716,12827,13060,13161,13256,13379,13508,13625,13802,13901,14036,14179,14314,14433,14634,14753,14846,14957,15013,15120,15315,15426,15559,15654,15745,15836,15929,16046,16185,16256,16339,16962,17019,17077,17701,18337"}, "to": {"startLines": "21,22,23,24,25,26,27,28,29,30,31,32,33,34,36,38,39,40,41,43,45,46,47,48,49,51,53,55,57,59,61,62,67,69,71,72,73,75,77,78,79,80,85,96,139,142,185,200,209,211,213,215,218,222,225,226,227,230,231,232,233,234,235,238,239,241,243,245,247,251,253,254,255,256,258,262,264,266,267,268,269,270,271,304,305,306,316,317,318,330", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1662,1753,1856,1959,2064,2171,2280,2389,2498,2607,2716,2823,2926,3045,3200,3355,3460,3581,3682,3829,3970,4073,4192,4299,4402,4557,4728,4877,5042,5199,5350,5469,5820,5969,6118,6230,6377,6530,6677,6752,6841,6928,7453,8424,11182,11367,14137,15270,15913,16036,16159,16272,16455,16710,16911,17000,17111,17344,17445,17540,17663,17792,17909,18086,18185,18320,18463,18598,18717,18918,19037,19130,19241,19297,19404,19599,19710,19843,19938,20029,20120,20213,20330,22849,22920,23003,23626,23683,23741,24365", "endLines": "21,22,23,24,25,26,27,28,29,30,31,32,33,35,37,38,39,40,42,44,45,46,47,48,50,52,54,56,58,60,61,66,68,70,71,72,74,76,77,78,79,80,85,138,141,184,187,202,210,212,214,217,221,224,225,226,229,230,231,232,233,234,237,238,240,242,244,246,250,252,253,254,255,257,261,263,265,266,267,268,269,270,272,304,305,315,316,317,329,341", "endColumns": "90,102,102,104,106,108,108,108,108,108,106,102,118,12,12,104,120,100,12,12,102,118,106,102,12,12,12,12,12,12,118,12,12,12,111,146,12,12,74,88,86,100,102,12,12,12,12,12,12,12,12,12,12,12,88,110,12,100,94,122,128,116,12,98,12,12,12,12,12,12,92,110,55,12,12,12,12,94,90,90,92,116,12,70,82,12,56,57,12,12", "endOffsets": "1748,1851,1954,2059,2166,2275,2384,2493,2602,2711,2818,2921,3040,3195,3350,3455,3576,3677,3824,3965,4068,4187,4294,4397,4552,4723,4872,5037,5194,5345,5464,5815,5964,6113,6225,6372,6525,6672,6747,6836,6923,7024,7551,11177,11362,14132,14329,15464,16031,16154,16267,16450,16705,16906,16995,17106,17339,17440,17535,17658,17787,17904,18081,18180,18315,18458,18593,18712,18913,19032,19125,19236,19292,19399,19594,19705,19838,19933,20024,20115,20208,20325,20464,22915,22998,23621,23678,23736,24360,24996"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f1013ccf4ee59567949af9e71a98b092\\transformed\\exoplayer-ui-2.18.1\\res\\values-v21\\values-v21.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,114", "endLines": "2,7", "endColumns": "58,10", "endOffsets": "109,396"}, "to": {"startLines": "3,282", "startColumns": "4,4", "startOffsets": "173,21076", "endLines": "3,286", "endColumns": "58,10", "endOffsets": "227,21358"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\d139f7b0159c383eb11b4ff91a7586c3\\transformed\\material-1.6.1\\res\\values-v21\\values-v21.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,24,27,30,33,36,39,42,45,48,49,52,57,68,74,84,94,104,114,124,134,144,154,164,174,184,194,204,214,224,230,236,242,248,252,256,257,258,259,263,266,269,272,275,278,281,285,289", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,153,249,345,443,511,590,678,766,854,942,1029,1116,1203,1290,1383,1490,1595,1714,1839,2052,2311,2582,2800,3032,3268,3518,3731,3962,4078,4248,4569,5598,6055,6608,7165,7723,8286,8840,9393,9947,10502,11053,11608,12166,12723,13271,13827,14384,14726,15070,15420,15770,16099,16440,16578,16722,16878,17271,17489,17711,17937,18153,18323,18513,18754,19013", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,23,26,29,32,35,38,41,44,47,48,51,56,67,73,83,93,103,113,123,133,143,153,163,173,183,193,203,213,223,229,235,241,247,251,255,256,257,258,262,265,268,271,274,277,280,284,288,291", "endColumns": "97,95,95,97,67,78,87,87,87,87,86,86,86,86,92,106,104,118,124,10,10,10,10,10,10,10,10,10,115,10,12,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,137,143,155,10,10,10,10,10,10,10,10,10,10", "endOffsets": "148,244,340,438,506,585,673,761,849,937,1024,1111,1198,1285,1378,1485,1590,1709,1834,2047,2306,2577,2795,3027,3263,3513,3726,3957,4073,4243,4564,5593,6050,6603,7160,7718,8281,8835,9388,9942,10497,11048,11603,12161,12718,13266,13822,14379,14721,15065,15415,15765,16094,16435,16573,16717,16873,17266,17484,17706,17932,18148,18318,18508,18749,19008,19185"}, "to": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,81,82,83,84,86,87,90,93,188,191,194,197,203,206,273,274,277,287,298,358,368,378,388,398,408,418,428,438,448,458,468,478,488,498,508,514,520,526,532,536,540,541,542,543,547,550,553,556,567,570,573,577,581", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "232,330,426,522,620,688,767,855,943,1031,1119,1206,1293,1380,7029,7122,7229,7334,7556,7681,7894,8153,14334,14552,14784,15020,15469,15682,20469,20585,20755,21363,22392,26163,26716,27273,27831,28394,28948,29501,30055,30610,31161,31716,32274,32831,33379,33935,34492,34784,35078,35378,35678,36007,36348,36486,36630,36786,37179,37397,37619,37845,38585,38755,38945,39186,39445", "endLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,81,82,83,84,86,89,92,95,190,193,196,199,205,208,273,276,281,297,303,367,377,387,397,407,417,427,437,447,457,467,477,487,497,507,513,519,525,531,535,539,540,541,542,546,549,552,555,558,569,572,576,580,583", "endColumns": "97,95,95,97,67,78,87,87,87,87,86,86,86,86,92,106,104,118,124,10,10,10,10,10,10,10,10,10,115,10,12,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,137,143,155,10,10,10,10,10,10,10,10,10,10", "endOffsets": "325,421,517,615,683,762,850,938,1026,1114,1201,1288,1375,1462,7117,7224,7329,7448,7676,7889,8148,8419,14547,14779,15015,15265,15677,15908,20580,20750,21071,22387,22844,26711,27268,27826,28389,28943,29496,30050,30605,31156,31711,32269,32826,33374,33930,34487,34779,35073,35373,35673,36002,36343,36481,36625,36781,37174,37392,37614,37840,38056,38750,38940,39181,39440,39617"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\71743d480ebb399fb4fe6b96265e7bca\\transformed\\core-1.13.1\\res\\values-v21\\values-v21.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,13", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,237,304,368,484,610,736,864,1036", "endLines": "2,3,4,5,6,7,8,9,12,17", "endColumns": "117,63,66,63,115,125,125,127,12,12", "endOffsets": "168,232,299,363,479,605,731,859,1031,1383"}, "to": {"startLines": "2,18,19,20,342,343,350,354,559,562", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,1467,1531,1598,25001,25117,25574,25868,38061,38233", "endLines": "2,18,19,20,342,343,350,354,561,566", "endColumns": "117,63,66,63,115,125,125,127,12,12", "endOffsets": "168,1526,1593,1657,25112,25238,25695,25991,38228,38580"}}]}]}