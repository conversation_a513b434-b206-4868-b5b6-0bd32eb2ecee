{"logs": [{"outputFile": "com.gokul719.snack97152fc1f368437dac54171df4ba22bd.app-mergeDebugResources-74:/values-hi/values-hi.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c53b383fa8484255d5fd97643f23aa97\\transformed\\foundation-release\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,140", "endColumns": "84,85", "endOffsets": "135,221"}, "to": {"startLines": "258,259", "startColumns": "4,4", "startOffsets": "20842,20927", "endColumns": "84,85", "endOffsets": "20922,21008"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\71743d480ebb399fb4fe6b96265e7bca\\transformed\\core-1.13.1\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,361,462,575,681,808", "endColumns": "97,102,104,100,112,105,126,100", "endOffsets": "148,251,356,457,570,676,803,904"}, "to": {"startLines": "52,53,54,55,56,57,58,250", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3740,3838,3941,4046,4147,4260,4366,20185", "endColumns": "97,102,104,100,112,105,126,100", "endOffsets": "3833,3936,4041,4142,4255,4361,4488,20281"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\68cda876f16b6c181c93767ccf9edd36\\transformed\\appcompat-1.7.0\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,211,309,419,505,607,728,806,883,974,1067,1162,1256,1356,1449,1544,1638,1729,1820,1901,2006,2108,2206,2316,2419,2528,2686,2787", "endColumns": "105,97,109,85,101,120,77,76,90,92,94,93,99,92,94,93,90,90,80,104,101,97,109,102,108,157,100,81", "endOffsets": "206,304,414,500,602,723,801,878,969,1062,1157,1251,1351,1444,1539,1633,1724,1815,1896,2001,2103,2201,2311,2414,2523,2681,2782,2864"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,237", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "908,1014,1112,1222,1308,1410,1531,1609,1686,1777,1870,1965,2059,2159,2252,2347,2441,2532,2623,2704,2809,2911,3009,3119,3222,3331,3489,19151", "endColumns": "105,97,109,85,101,120,77,76,90,92,94,93,99,92,94,93,90,90,80,104,101,97,109,102,108,157,100,81", "endOffsets": "1009,1107,1217,1303,1405,1526,1604,1681,1772,1865,1960,2054,2154,2247,2342,2436,2527,2618,2699,2804,2906,3004,3114,3217,3326,3484,3585,19228"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\3767e343142fab2c839a998d5bfdc1ca\\transformed\\play-services-ads-22.6.0\\res\\values-hi\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "199,248,296,350,418,488,589,670,782,836,946,1002,1122,1203,1244,1340,1379,1417,1464,1528,1569", "endColumns": "48,47,53,67,69,100,80,111,53,109,55,119,80,40,95,38,37,46,63,40,55", "endOffsets": "247,295,349,417,487,588,669,781,835,945,1001,1121,1202,1243,1339,1378,1416,1463,1527,1568,1624"}, "to": {"startLines": "207,208,209,212,213,214,215,216,217,218,219,220,221,229,230,231,232,233,234,235,260", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "16906,16959,17011,17243,17315,17389,17494,17579,17695,17753,17867,17927,18051,18678,18723,18823,18866,18908,18959,19027,21013", "endColumns": "52,51,57,71,73,104,84,115,57,113,59,123,84,44,99,42,41,50,67,44,59", "endOffsets": "16954,17006,17064,17310,17384,17489,17574,17690,17748,17862,17922,18046,18131,18718,18818,18861,18903,18954,19022,19067,21068"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\268d4161c8b8edb1216e3f661bb7ee02\\transformed\\browser-1.6.0\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,161,263,375", "endColumns": "105,101,111,102", "endOffsets": "156,258,370,473"}, "to": {"startLines": "83,143,144,145", "startColumns": "4,4,4,4", "startOffsets": "7303,11892,11994,12106", "endColumns": "105,101,111,102", "endOffsets": "7404,11989,12101,12204"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\51b16512a656ca1125c5acd36144cb6d\\transformed\\exoplayer-core-2.18.1\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,123,189,260,328,424,492,615,736", "endColumns": "67,65,70,67,95,67,122,120,86", "endOffsets": "118,184,255,323,419,487,610,731,818"}, "to": {"startLines": "115,116,117,118,119,120,121,122,123", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "9900,9968,10034,10105,10173,10269,10337,10460,10581", "endColumns": "67,65,70,67,95,67,122,120,86", "endOffsets": "9963,10029,10100,10168,10264,10332,10455,10576,10663"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\d139f7b0159c383eb11b4ff91a7586c3\\transformed\\material-1.6.1\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,214,295,402,534,617,682,776,845,904,989,1052,1110,1175,1236,1297,1403,1461,1521,1580,1650,1766,1845,1925,2029,2104,2180,2277,2344,2410,2480,2557,2643,2711,2787,2868,2946,3032,3119,3216,3315,3389,3459,3563,3617,3684,3774,3866,3928,3992,4055,4160,4268,4369,4478", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,80,106,131,82,64,93,68,58,84,62,57,64,60,60,105,57,59,58,69,115,78,79,103,74,75,96,66,65,69,76,85,67,75,80,77,85,86,96,98,73,69,103,53,66,89,91,61,63,62,104,107,100,108,78", "endOffsets": "209,290,397,529,612,677,771,840,899,984,1047,1105,1170,1231,1292,1398,1456,1516,1575,1645,1761,1840,1920,2024,2099,2175,2272,2339,2405,2475,2552,2638,2706,2782,2863,2941,3027,3114,3211,3310,3384,3454,3558,3612,3679,3769,3861,3923,3987,4050,4155,4263,4364,4473,4552"}, "to": {"startLines": "19,51,59,60,61,90,142,152,157,159,160,161,162,163,164,165,166,167,168,169,170,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,222", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "749,3659,4493,4600,4732,7896,11798,12596,12989,13115,13200,13263,13321,13386,13447,13508,13614,13672,13732,13791,13861,14194,14273,14353,14457,14532,14608,14705,14772,14838,14908,14985,15071,15139,15215,15296,15374,15460,15547,15644,15743,15817,15887,15991,16045,16112,16202,16294,16356,16420,16483,16588,16696,16797,18136", "endLines": "22,51,59,60,61,90,142,152,157,159,160,161,162,163,164,165,166,167,168,169,170,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,222", "endColumns": "12,80,106,131,82,64,93,68,58,84,62,57,64,60,60,105,57,59,58,69,115,78,79,103,74,75,96,66,65,69,76,85,67,75,80,77,85,86,96,98,73,69,103,53,66,89,91,61,63,62,104,107,100,108,78", "endOffsets": "903,3735,4595,4727,4810,7956,11887,12660,13043,13195,13258,13316,13381,13442,13503,13609,13667,13727,13786,13856,13972,14268,14348,14452,14527,14603,14700,14767,14833,14903,14980,15066,15134,15210,15291,15369,15455,15542,15639,15738,15812,15882,15986,16040,16107,16197,16289,16351,16415,16478,16583,16691,16792,16901,18210"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f1013ccf4ee59567949af9e71a98b092\\transformed\\exoplayer-ui-2.18.1\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,493,699,782,863,946,1041,1141,1210,1273,1359,1445,1510,1574,1638,1706,1819,1935,2047,2120,2204,2273,2342,2426,2508,2575,2638,2691,2753,2807,2868,2928,2995,3058,3128,3189,3251,3317,3380,3447,3507,3567,3641,3715", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,82,80,82,94,99,68,62,85,85,64,63,63,67,112,115,111,72,83,68,68,83,81,66,62,52,61,53,60,59,66,62,69,60,61,65,62,66,59,59,73,73,52", "endOffsets": "281,488,694,777,858,941,1036,1136,1205,1268,1354,1440,1505,1569,1633,1701,1814,1930,2042,2115,2199,2268,2337,2421,2503,2570,2633,2686,2748,2802,2863,2923,2990,3053,3123,3184,3246,3312,3375,3442,3502,3562,3636,3710,3763"}, "to": {"startLines": "2,11,15,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,336,543,7961,8044,8125,8208,8303,8403,8472,8535,8621,8707,8772,8836,8900,8968,9081,9197,9309,9382,9466,9535,9604,9688,9770,9837,10668,10721,10783,10837,10898,10958,11025,11088,11158,11219,11281,11347,11410,11477,11537,11597,11671,11745", "endLines": "10,14,18,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141", "endColumns": "17,12,12,82,80,82,94,99,68,62,85,85,64,63,63,67,112,115,111,72,83,68,68,83,81,66,62,52,61,53,60,59,66,62,69,60,61,65,62,66,59,59,73,73,52", "endOffsets": "331,538,744,8039,8120,8203,8298,8398,8467,8530,8616,8702,8767,8831,8895,8963,9076,9192,9304,9377,9461,9530,9599,9683,9765,9832,9895,10716,10778,10832,10893,10953,11020,11083,11153,11214,11276,11342,11405,11472,11532,11592,11666,11740,11793"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\263c8e8847d6991526302086ecc6e182\\transformed\\play-services-base-18.0.1\\res\\values-hi\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,297,453,575,683,830,956,1064,1172,1325,1430,1592,1718,1855,2004,2063,2126", "endColumns": "103,155,121,107,146,125,107,107,152,104,161,125,136,148,58,62,83", "endOffsets": "296,452,574,682,829,955,1063,1171,1324,1429,1591,1717,1854,2003,2062,2125,2209"}, "to": {"startLines": "65,66,67,68,69,70,71,72,74,75,76,77,78,79,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5072,5180,5340,5466,5578,5729,5859,5971,6229,6386,6495,6661,6791,6932,7085,7148,7215", "endColumns": "107,159,125,111,150,129,111,111,156,108,165,129,140,152,62,66,87", "endOffsets": "5175,5335,5461,5573,5724,5854,5966,6078,6381,6490,6656,6786,6927,7080,7143,7210,7298"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\37f986109817451798d14c7952a3b6e1\\transformed\\react-android-0.76.9-debug\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,203,274,343,423,490,557,631,707,790,871,939,1018,1096,1171,1258,1344,1419,1493,1567,1654,1726,1801,1870", "endColumns": "68,78,70,68,79,66,66,73,75,82,80,67,78,77,74,86,85,74,73,73,86,71,74,68,72", "endOffsets": "119,198,269,338,418,485,552,626,702,785,866,934,1013,1091,1166,1253,1339,1414,1488,1562,1649,1721,1796,1865,1938"}, "to": {"startLines": "50,64,146,153,154,158,171,172,173,224,225,228,236,239,240,241,243,244,246,248,249,251,254,256,257", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3590,4993,12209,12665,12734,13048,13977,14044,14118,18280,18363,18610,19072,19306,19384,19459,19639,19725,19875,20024,20098,20286,20505,20700,20769", "endColumns": "68,78,70,68,79,66,66,73,75,82,80,67,78,77,74,86,85,74,73,73,86,71,74,68,72", "endOffsets": "3654,5067,12275,12729,12809,13110,14039,14113,14189,18358,18439,18673,19146,19379,19454,19541,19720,19795,19944,20093,20180,20353,20575,20764,20837"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\02b86cfeeba2fc323c383047854b9491\\transformed\\play-services-basement-18.2.0\\res\\values-hi\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "141", "endOffsets": "336"}, "to": {"startLines": "73", "startColumns": "4", "startOffsets": "6083", "endColumns": "145", "endOffsets": "6224"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\084b6a12791f1cb8a95d16811ba661d8\\transformed\\Android-Image-Cropper-4.3.1\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,161,208,262,307,373,440,524,578", "endColumns": "105,46,53,44,65,66,83,53,64", "endOffsets": "156,203,257,302,368,435,519,573,638"}, "to": {"startLines": "84,85,86,147,148,149,150,151,223", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "7409,7515,7562,12280,12325,12391,12458,12542,18215", "endColumns": "105,46,53,44,65,66,83,53,64", "endOffsets": "7510,7557,7611,12320,12386,12453,12537,12591,18275"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\491ff7541ddc657f2c55a259a03f9653\\transformed\\ui-release\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,283,376,474,563,641,738,827,912,993,1078,1151,1244,1319,1394,1475,1541", "endColumns": "94,82,92,97,88,77,96,88,84,80,84,72,92,74,74,80,65,119", "endOffsets": "195,278,371,469,558,636,733,822,907,988,1073,1146,1239,1314,1389,1470,1536,1656"}, "to": {"startLines": "62,63,87,88,89,155,156,210,211,226,227,238,242,245,247,252,253,255", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4815,4910,7616,7709,7807,12814,12892,17069,17158,18444,18525,19233,19546,19800,19949,20358,20439,20580", "endColumns": "94,82,92,97,88,77,96,88,84,80,84,72,92,74,74,80,65,119", "endOffsets": "4905,4988,7704,7802,7891,12887,12984,17153,17238,18520,18605,19301,19634,19870,20019,20434,20500,20695"}}]}]}