{"logs": [{"outputFile": "com.gokul719.snack97152fc1f368437dac54171df4ba22bd.app-mergeDebugResources-74:/values-fa/values-fa.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c53b383fa8484255d5fd97643f23aa97\\transformed\\foundation-release\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,144", "endColumns": "88,88", "endOffsets": "139,228"}, "to": {"startLines": "258,259", "startColumns": "4,4", "startOffsets": "20553,20642", "endColumns": "88,88", "endOffsets": "20637,20726"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\02b86cfeeba2fc323c383047854b9491\\transformed\\play-services-basement-18.2.0\\res\\values-fa\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "150", "endOffsets": "345"}, "to": {"startLines": "73", "startColumns": "4", "startOffsets": "6002", "endColumns": "154", "endOffsets": "6152"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\51b16512a656ca1125c5acd36144cb6d\\transformed\\exoplayer-core-2.18.1\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,122,183,250,310,388,463,552,640", "endColumns": "66,60,66,59,77,74,88,87,64", "endOffsets": "117,178,245,305,383,458,547,635,700"}, "to": {"startLines": "115,116,117,118,119,120,121,122,123", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "9852,9919,9980,10047,10107,10185,10260,10349,10437", "endColumns": "66,60,66,59,77,74,88,87,64", "endOffsets": "9914,9975,10042,10102,10180,10255,10344,10432,10497"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\d139f7b0159c383eb11b4ff91a7586c3\\transformed\\material-1.6.1\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,214,296,393,522,606,669,759,828,888,979,1043,1102,1169,1231,1286,1409,1467,1528,1583,1655,1792,1873,1955,2055,2129,2203,2289,2356,2422,2493,2570,2651,2724,2798,2868,2942,3028,3102,3191,3283,3357,3430,3519,3570,3637,3720,3804,3866,3930,3993,4087,4194,4287,4392", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,81,96,128,83,62,89,68,59,90,63,58,66,61,54,122,57,60,54,71,136,80,81,99,73,73,85,66,65,70,76,80,72,73,69,73,85,73,88,91,73,72,88,50,66,82,83,61,63,62,93,106,92,104,77", "endOffsets": "209,291,388,517,601,664,754,823,883,974,1038,1097,1164,1226,1281,1404,1462,1523,1578,1650,1787,1868,1950,2050,2124,2198,2284,2351,2417,2488,2565,2646,2719,2793,2863,2937,3023,3097,3186,3278,3352,3425,3514,3565,3632,3715,3799,3861,3925,3988,4082,4189,4282,4387,4465"}, "to": {"startLines": "19,51,59,60,61,90,142,152,157,159,160,161,162,163,164,165,166,167,168,169,170,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,222", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "746,3664,4470,4567,4696,7832,11626,12425,12829,12957,13048,13112,13171,13238,13300,13355,13478,13536,13597,13652,13724,14076,14157,14239,14339,14413,14487,14573,14640,14706,14777,14854,14935,15008,15082,15152,15226,15312,15386,15475,15567,15641,15714,15803,15854,15921,16004,16088,16150,16214,16277,16371,16478,16571,17888", "endLines": "22,51,59,60,61,90,142,152,157,159,160,161,162,163,164,165,166,167,168,169,170,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,222", "endColumns": "12,81,96,128,83,62,89,68,59,90,63,58,66,61,54,122,57,60,54,71,136,80,81,99,73,73,85,66,65,70,76,80,72,73,69,73,85,73,88,91,73,72,88,50,66,82,83,61,63,62,93,106,92,104,77", "endOffsets": "900,3741,4562,4691,4775,7890,11711,12489,12884,13043,13107,13166,13233,13295,13350,13473,13531,13592,13647,13719,13856,14152,14234,14334,14408,14482,14568,14635,14701,14772,14849,14930,15003,15077,15147,15221,15307,15381,15470,15562,15636,15709,15798,15849,15916,15999,16083,16145,16209,16272,16366,16473,16566,16671,17961"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\491ff7541ddc657f2c55a259a03f9653\\transformed\\ui-release\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,192,271,365,463,549,631,734,819,902,983,1065,1139,1214,1288,1360,1435,1502", "endColumns": "86,78,93,97,85,81,102,84,82,80,81,73,74,73,71,74,66,116", "endOffsets": "187,266,360,458,544,626,729,814,897,978,1060,1134,1209,1283,1355,1430,1497,1614"}, "to": {"startLines": "62,63,87,88,89,155,156,210,211,226,227,238,242,245,247,252,253,255", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4780,4867,7554,7648,7746,12644,12726,16839,16924,18201,18282,18977,19285,19520,19667,20070,20145,20288", "endColumns": "86,78,93,97,85,81,102,84,82,80,81,73,74,73,71,74,66,116", "endOffsets": "4862,4941,7643,7741,7827,12721,12824,16919,17002,18277,18359,19046,19355,19589,19734,20140,20207,20400"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\263c8e8847d6991526302086ecc6e182\\transformed\\play-services-base-18.0.1\\res\\values-fa\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,297,450,575,674,810,932,1042,1140,1289,1395,1561,1688,1837,1989,2051,2115", "endColumns": "103,152,124,98,135,121,109,97,148,105,165,126,148,151,61,63,80", "endOffsets": "296,449,574,673,809,931,1041,1139,1288,1394,1560,1687,1836,1988,2050,2114,2195"}, "to": {"startLines": "65,66,67,68,69,70,71,72,74,75,76,77,78,79,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5023,5131,5288,5417,5520,5660,5786,5900,6157,6310,6420,6590,6721,6874,7030,7096,7164", "endColumns": "107,156,128,102,139,125,113,101,152,109,169,130,152,155,65,67,84", "endOffsets": "5126,5283,5412,5515,5655,5781,5895,5997,6305,6415,6585,6716,6869,7025,7091,7159,7244"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\268d4161c8b8edb1216e3f661bb7ee02\\transformed\\browser-1.6.0\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,154,251,362", "endColumns": "98,96,110,102", "endOffsets": "149,246,357,460"}, "to": {"startLines": "83,143,144,145", "startColumns": "4,4,4,4", "startOffsets": "7249,11716,11813,11924", "endColumns": "98,96,110,102", "endOffsets": "7343,11808,11919,12022"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\37f986109817451798d14c7952a3b6e1\\transformed\\react-android-0.76.9-debug\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,201,273,342,423,491,557,631,706,787,868,937,1016,1094,1168,1250,1331,1410,1483,1554,1642,1713,1789,1861", "endColumns": "68,76,71,68,80,67,65,73,74,80,80,68,78,77,73,81,80,78,72,70,87,70,75,71,75", "endOffsets": "119,196,268,337,418,486,552,626,701,782,863,932,1011,1089,1163,1245,1326,1405,1478,1549,1637,1708,1784,1856,1932"}, "to": {"startLines": "50,64,146,153,154,158,171,172,173,224,225,228,236,239,240,241,243,244,246,248,249,251,254,256,257", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3595,4946,12027,12494,12563,12889,13861,13927,14001,18039,18120,18364,18816,19051,19129,19203,19360,19441,19594,19739,19810,19999,20212,20405,20477", "endColumns": "68,76,71,68,80,67,65,73,74,80,80,68,78,77,73,81,80,78,72,70,87,70,75,71,75", "endOffsets": "3659,5018,12094,12558,12639,12952,13922,13996,14071,18115,18196,18428,18890,19124,19198,19280,19436,19515,19662,19805,19893,20065,20283,20472,20548"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\71743d480ebb399fb4fe6b96265e7bca\\transformed\\core-1.13.1\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,355,455,556,662,779", "endColumns": "98,101,98,99,100,105,116,100", "endOffsets": "149,251,350,450,551,657,774,875"}, "to": {"startLines": "52,53,54,55,56,57,58,250", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3746,3845,3947,4046,4146,4247,4353,19898", "endColumns": "98,101,98,99,100,105,116,100", "endOffsets": "3840,3942,4041,4141,4242,4348,4465,19994"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f1013ccf4ee59567949af9e71a98b092\\transformed\\exoplayer-ui-2.18.1\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,313,512,696,779,864,943,1036,1128,1205,1268,1360,1447,1510,1572,1633,1700,1816,1937,2057,2126,2202,2272,2344,2429,2516,2579,2653,2707,2776,2824,2885,2943,3020,3084,3148,3208,3270,3335,3401,3467,3519,3578,3651,3724", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,82,84,78,92,91,76,62,91,86,62,61,60,66,115,120,119,68,75,69,71,84,86,62,73,53,68,47,60,57,76,63,63,59,61,64,65,65,51,58,72,72,52", "endOffsets": "308,507,691,774,859,938,1031,1123,1200,1263,1355,1442,1505,1567,1628,1695,1811,1932,2052,2121,2197,2267,2339,2424,2511,2574,2648,2702,2771,2819,2880,2938,3015,3079,3143,3203,3265,3330,3396,3462,3514,3573,3646,3719,3772"}, "to": {"startLines": "2,11,15,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,363,562,7895,7978,8063,8142,8235,8327,8404,8467,8559,8646,8709,8771,8832,8899,9015,9136,9256,9325,9401,9471,9543,9628,9715,9778,10502,10556,10625,10673,10734,10792,10869,10933,10997,11057,11119,11184,11250,11316,11368,11427,11500,11573", "endLines": "10,14,18,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141", "endColumns": "17,12,12,82,84,78,92,91,76,62,91,86,62,61,60,66,115,120,119,68,75,69,71,84,86,62,73,53,68,47,60,57,76,63,63,59,61,64,65,65,51,58,72,72,52", "endOffsets": "358,557,741,7973,8058,8137,8230,8322,8399,8462,8554,8641,8704,8766,8827,8894,9010,9131,9251,9320,9396,9466,9538,9623,9710,9773,9847,10551,10620,10668,10729,10787,10864,10928,10992,11052,11114,11179,11245,11311,11363,11422,11495,11568,11621"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\3767e343142fab2c839a998d5bfdc1ca\\transformed\\play-services-ads-22.6.0\\res\\values-fa\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "199,247,292,350,418,487,592,673,772,825,938,995,1104,1191,1230,1315,1350,1386,1432,1506,1546", "endColumns": "47,44,57,67,68,104,80,98,52,112,56,108,86,38,84,34,35,45,73,39,55", "endOffsets": "246,291,349,417,486,591,672,771,824,937,994,1103,1190,1229,1314,1349,1385,1431,1505,1545,1601"}, "to": {"startLines": "207,208,209,212,213,214,215,216,217,218,219,220,221,229,230,231,232,233,234,235,260", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "16676,16728,16777,17007,17079,17152,17261,17346,17449,17506,17623,17684,17797,18433,18476,18565,18604,18644,18694,18772,20731", "endColumns": "51,48,61,71,72,108,84,102,56,116,60,112,90,42,88,38,39,49,77,43,59", "endOffsets": "16723,16772,16834,17074,17147,17256,17341,17444,17501,17618,17679,17792,17883,18471,18560,18599,18639,18689,18767,18811,20786"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\68cda876f16b6c181c93767ccf9edd36\\transformed\\appcompat-1.7.0\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,215,316,427,511,612,727,807,884,977,1072,1164,1258,1360,1455,1552,1646,1739,1829,1911,2019,2123,2221,2327,2432,2537,2694,2795", "endColumns": "109,100,110,83,100,114,79,76,92,94,91,93,101,94,96,93,92,89,81,107,103,97,105,104,104,156,100,81", "endOffsets": "210,311,422,506,607,722,802,879,972,1067,1159,1253,1355,1450,1547,1641,1734,1824,1906,2014,2118,2216,2322,2427,2532,2689,2790,2872"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,237", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "905,1015,1116,1227,1311,1412,1527,1607,1684,1777,1872,1964,2058,2160,2255,2352,2446,2539,2629,2711,2819,2923,3021,3127,3232,3337,3494,18895", "endColumns": "109,100,110,83,100,114,79,76,92,94,91,93,101,94,96,93,92,89,81,107,103,97,105,104,104,156,100,81", "endOffsets": "1010,1111,1222,1306,1407,1522,1602,1679,1772,1867,1959,2053,2155,2250,2347,2441,2534,2624,2706,2814,2918,3016,3122,3227,3332,3489,3590,18972"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\084b6a12791f1cb8a95d16811ba661d8\\transformed\\Android-Image-Cropper-4.3.1\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,152,199,261,311,387,462,535,587", "endColumns": "96,46,61,49,75,74,72,51,72", "endOffsets": "147,194,256,306,382,457,530,582,655"}, "to": {"startLines": "84,85,86,147,148,149,150,151,223", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "7348,7445,7492,12099,12149,12225,12300,12373,17966", "endColumns": "96,46,61,49,75,74,72,51,72", "endOffsets": "7440,7487,7549,12144,12220,12295,12368,12420,18034"}}]}]}