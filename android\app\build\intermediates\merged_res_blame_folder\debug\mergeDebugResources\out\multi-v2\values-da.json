{"logs": [{"outputFile": "com.gokul719.snack97152fc1f368437dac54171df4ba22bd.app-mergeDebugResources-74:/values-da/values-da.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\37f986109817451798d14c7952a3b6e1\\transformed\\react-android-0.76.9-debug\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,131,214,289,360,443,518,594,675,755,824,902,981,1057,1137,1217,1294,1365,1435,1518,1592,1674", "endColumns": "75,82,74,70,82,74,75,80,79,68,77,78,75,79,79,76,70,69,82,73,81,78", "endOffsets": "126,209,284,355,438,513,589,670,750,819,897,976,1052,1132,1212,1289,1360,1430,1513,1587,1669,1748"}, "to": {"startLines": "50,64,143,145,146,162,163,213,214,217,225,228,229,230,232,233,235,237,238,240,243,245", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3538,4918,11794,11942,12013,13216,13291,17259,17340,17585,18050,18278,18357,18433,18587,18667,18816,18958,19028,19212,19431,19633", "endColumns": "75,82,74,70,82,74,75,80,79,68,77,78,75,79,79,76,70,69,82,73,81,78", "endOffsets": "3609,4996,11864,12008,12091,13286,13362,17335,17415,17649,18123,18352,18428,18508,18662,18739,18882,19023,19106,19281,19508,19707"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\68cda876f16b6c181c93767ccf9edd36\\transformed\\appcompat-1.7.0\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,205,299,415,500,600,713,791,867,958,1051,1144,1238,1332,1425,1520,1618,1709,1800,1879,1987,2094,2190,2303,2406,2507,2660,2757", "endColumns": "99,93,115,84,99,112,77,75,90,92,92,93,93,92,94,97,90,90,78,107,106,95,112,102,100,152,96,79", "endOffsets": "200,294,410,495,595,708,786,862,953,1046,1139,1233,1327,1420,1515,1613,1704,1795,1874,1982,2089,2185,2298,2401,2502,2655,2752,2832"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,226", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "886,986,1080,1196,1281,1381,1494,1572,1648,1739,1832,1925,2019,2113,2206,2301,2399,2490,2581,2660,2768,2875,2971,3084,3187,3288,3441,18128", "endColumns": "99,93,115,84,99,112,77,75,90,92,92,93,93,92,94,97,90,90,78,107,106,95,112,102,100,152,96,79", "endOffsets": "981,1075,1191,1276,1376,1489,1567,1643,1734,1827,1920,2014,2108,2201,2296,2394,2485,2576,2655,2763,2870,2966,3079,3182,3283,3436,3533,18203"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\d139f7b0159c383eb11b4ff91a7586c3\\transformed\\material-1.6.1\\res\\values-da\\values-da.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,227,305,405,554,632,696,782,855,915,1002,1064,1126,1194,1259,1315,1433,1491,1552,1608,1683,1809,1895,1975,2086,2164,2244,2330,2397,2463,2531,2605,2694,2766,2844,2914,2987,3071,3148,3236,3325,3399,3472,3557,3606,3672,3752,3835,3897,3961,4024,4132,4227,4328,4423", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,77,99,148,77,63,85,72,59,86,61,61,67,64,55,117,57,60,55,74,125,85,79,110,77,79,85,66,65,67,73,88,71,77,69,72,83,76,87,88,73,72,84,48,65,79,82,61,63,62,107,94,100,94,79", "endOffsets": "222,300,400,549,627,691,777,850,910,997,1059,1121,1189,1254,1310,1428,1486,1547,1603,1678,1804,1890,1970,2081,2159,2239,2325,2392,2458,2526,2600,2689,2761,2839,2909,2982,3066,3143,3231,3320,3394,3467,3552,3601,3667,3747,3830,3892,3956,4019,4127,4222,4323,4418,4498"}, "to": {"startLines": "19,51,59,60,61,87,139,144,149,150,151,152,153,154,155,156,157,158,159,160,161,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,212", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "714,3614,4419,4519,4668,7591,11405,11869,12262,12322,12409,12471,12533,12601,12666,12722,12840,12898,12959,13015,13090,13367,13453,13533,13644,13722,13802,13888,13955,14021,14089,14163,14252,14324,14402,14472,14545,14629,14706,14794,14883,14957,15030,15115,15164,15230,15310,15393,15455,15519,15582,15690,15785,15886,17179", "endLines": "22,51,59,60,61,87,139,144,149,150,151,152,153,154,155,156,157,158,159,160,161,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,212", "endColumns": "12,77,99,148,77,63,85,72,59,86,61,61,67,64,55,117,57,60,55,74,125,85,79,110,77,79,85,66,65,67,73,88,71,77,69,72,83,76,87,88,73,72,84,48,65,79,82,61,63,62,107,94,100,94,79", "endOffsets": "881,3687,4514,4663,4741,7650,11486,11937,12317,12404,12466,12528,12596,12661,12717,12835,12893,12954,13010,13085,13211,13448,13528,13639,13717,13797,13883,13950,14016,14084,14158,14247,14319,14397,14467,14540,14624,14701,14789,14878,14952,15025,15110,15159,15225,15305,15388,15450,15514,15577,15685,15780,15881,15976,17254"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\268d4161c8b8edb1216e3f661bb7ee02\\transformed\\browser-1.6.0\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,167,266,373", "endColumns": "111,98,106,96", "endOffsets": "162,261,368,465"}, "to": {"startLines": "83,140,141,142", "startColumns": "4,4,4,4", "startOffsets": "7203,11491,11590,11697", "endColumns": "111,98,106,96", "endOffsets": "7310,11585,11692,11789"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f1013ccf4ee59567949af9e71a98b092\\transformed\\exoplayer-ui-2.18.1\\res\\values-da\\values-da.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,474,664,750,837,912,997,1084,1155,1219,1317,1413,1485,1550,1616,1686,1793,1900,2005,2078,2158,2234,2303,2382,2462,2525,2593,2646,2704,2752,2813,2883,2955,3023,3097,3161,3220,3284,3354,3420,3472,3533,3609,3684", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,85,86,74,84,86,70,63,97,95,71,64,65,69,106,106,104,72,79,75,68,78,79,62,67,52,57,47,60,69,71,67,73,63,58,63,69,65,51,60,75,74,52", "endOffsets": "280,469,659,745,832,907,992,1079,1150,1214,1312,1408,1480,1545,1611,1681,1788,1895,2000,2073,2153,2229,2298,2377,2457,2520,2588,2641,2699,2747,2808,2878,2950,3018,3092,3156,3215,3279,3349,3415,3467,3528,3604,3679,3732"}, "to": {"startLines": "2,11,15,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,335,524,7655,7741,7828,7903,7988,8075,8146,8210,8308,8404,8476,8541,8607,8677,8784,8891,8996,9069,9149,9225,9294,9373,9453,9516,10261,10314,10372,10420,10481,10551,10623,10691,10765,10829,10888,10952,11022,11088,11140,11201,11277,11352", "endLines": "10,14,18,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138", "endColumns": "17,12,12,85,86,74,84,86,70,63,97,95,71,64,65,69,106,106,104,72,79,75,68,78,79,62,67,52,57,47,60,69,71,67,73,63,58,63,69,65,51,60,75,74,52", "endOffsets": "330,519,709,7736,7823,7898,7983,8070,8141,8205,8303,8399,8471,8536,8602,8672,8779,8886,8991,9064,9144,9220,9289,9368,9448,9511,9579,10309,10367,10415,10476,10546,10618,10686,10760,10824,10883,10947,11017,11083,11135,11196,11272,11347,11400"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\71743d480ebb399fb4fe6b96265e7bca\\transformed\\core-1.13.1\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,350,448,555,664,782", "endColumns": "95,101,96,97,106,108,117,100", "endOffsets": "146,248,345,443,550,659,777,878"}, "to": {"startLines": "52,53,54,55,56,57,58,239", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3692,3788,3890,3987,4085,4192,4301,19111", "endColumns": "95,101,96,97,106,108,117,100", "endOffsets": "3783,3885,3982,4080,4187,4296,4414,19207"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\51b16512a656ca1125c5acd36144cb6d\\transformed\\exoplayer-core-2.18.1\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,127,189,253,322,399,473,573,664", "endColumns": "71,61,63,68,76,73,99,90,67", "endOffsets": "122,184,248,317,394,468,568,659,727"}, "to": {"startLines": "112,113,114,115,116,117,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "9584,9656,9718,9782,9851,9928,10002,10102,10193", "endColumns": "71,61,63,68,76,73,99,90,67", "endOffsets": "9651,9713,9777,9846,9923,9997,10097,10188,10256"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\263c8e8847d6991526302086ecc6e182\\transformed\\play-services-base-18.0.1\\res\\values-da\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,451,574,679,818,939,1055,1156,1308,1410,1568,1691,1832,2006,2068,2126", "endColumns": "101,155,122,104,138,120,115,100,151,101,157,122,140,173,61,57,73", "endOffsets": "294,450,573,678,817,938,1054,1155,1307,1409,1567,1690,1831,2005,2067,2125,2199"}, "to": {"startLines": "65,66,67,68,69,70,71,72,74,75,76,77,78,79,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5001,5107,5267,5394,5503,5646,5771,5891,6123,6279,6385,6547,6674,6819,6997,7063,7125", "endColumns": "105,159,126,108,142,124,119,104,155,105,161,126,144,177,65,61,77", "endOffsets": "5102,5262,5389,5498,5641,5766,5886,5991,6274,6380,6542,6669,6814,6992,7058,7120,7198"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\491ff7541ddc657f2c55a259a03f9653\\transformed\\ui-release\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,197,277,372,471,553,630,719,808,890,971,1055,1125,1199,1271,1342,1420,1487", "endColumns": "91,79,94,98,81,76,88,88,81,80,83,69,73,71,70,77,66,119", "endOffsets": "192,272,367,466,548,625,714,803,885,966,1050,1120,1194,1266,1337,1415,1482,1602"}, "to": {"startLines": "62,63,84,85,86,147,148,200,201,215,216,227,231,234,236,241,242,244", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4746,4838,7315,7410,7509,12096,12173,16145,16234,17420,17501,18208,18513,18744,18887,19286,19364,19513", "endColumns": "91,79,94,98,81,76,88,88,81,80,83,69,73,71,70,77,66,119", "endOffsets": "4833,4913,7405,7504,7586,12168,12257,16229,16311,17496,17580,18273,18582,18811,18953,19359,19426,19628"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\02b86cfeeba2fc323c383047854b9491\\transformed\\play-services-basement-18.2.0\\res\\values-da\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "122", "endOffsets": "317"}, "to": {"startLines": "73", "startColumns": "4", "startOffsets": "5996", "endColumns": "126", "endOffsets": "6118"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\3767e343142fab2c839a998d5bfdc1ca\\transformed\\play-services-ads-22.6.0\\res\\values-da\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "199,245,296,351,415,484,591,667,764,814,923,978,1092,1174,1213,1295,1331,1364,1416,1503,1542", "endColumns": "45,50,54,63,68,106,75,96,49,108,54,113,81,38,81,35,32,51,86,38,55", "endOffsets": "244,295,350,414,483,590,666,763,813,922,977,1091,1173,1212,1294,1330,1363,1415,1502,1541,1597"}, "to": {"startLines": "197,198,199,202,203,204,205,206,207,208,209,210,211,218,219,220,221,222,223,224,248", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15981,16031,16086,16316,16384,16457,16568,16648,16749,16803,16916,16975,17093,17654,17697,17783,17823,17860,17916,18007,19889", "endColumns": "49,54,58,67,72,110,79,100,53,112,58,117,85,42,85,39,36,55,90,42,59", "endOffsets": "16026,16081,16140,16379,16452,16563,16643,16744,16798,16911,16970,17088,17174,17692,17778,17818,17855,17911,18002,18045,19944"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c53b383fa8484255d5fd97643f23aa97\\transformed\\foundation-release\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,145", "endColumns": "89,86", "endOffsets": "140,227"}, "to": {"startLines": "246,247", "startColumns": "4,4", "startOffsets": "19712,19802", "endColumns": "89,86", "endOffsets": "19797,19884"}}]}]}