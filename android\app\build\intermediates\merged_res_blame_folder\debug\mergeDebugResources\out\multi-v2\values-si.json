{"logs": [{"outputFile": "com.gokul719.snack97152fc1f368437dac54171df4ba22bd.app-mergeDebugResources-74:/values-si/values-si.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c53b383fa8484255d5fd97643f23aa97\\transformed\\foundation-release\\res\\values-si\\values-si.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,145", "endColumns": "89,92", "endOffsets": "140,233"}, "to": {"startLines": "249,250", "startColumns": "4,4", "startOffsets": "20131,20221", "endColumns": "89,92", "endOffsets": "20216,20309"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f1013ccf4ee59567949af9e71a98b092\\transformed\\exoplayer-ui-2.18.1\\res\\values-si\\values-si.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,287,488,666,750,833,914,1007,1099,1162,1224,1313,1404,1475,1545,1606,1672,1811,1953,2090,2161,2240,2310,2375,2465,2554,2621,2689,2742,2800,2847,2908,2968,3035,3096,3161,3220,3285,3354,3417,3484,3538,3595,3666,3737", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,83,82,80,92,91,62,61,88,90,70,69,60,65,138,141,136,70,78,69,64,89,88,66,67,52,57,46,60,59,66,60,64,58,64,68,62,66,53,56,70,70,51", "endOffsets": "282,483,661,745,828,909,1002,1094,1157,1219,1308,1399,1470,1540,1601,1667,1806,1948,2085,2156,2235,2305,2370,2460,2549,2616,2684,2737,2795,2842,2903,2963,3030,3091,3156,3215,3280,3349,3412,3479,3533,3590,3661,3732,3784"}, "to": {"startLines": "2,11,15,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,337,538,7676,7760,7843,7924,8017,8109,8172,8234,8323,8414,8485,8555,8616,8682,8821,8963,9100,9171,9250,9320,9385,9475,9564,9631,10354,10407,10465,10512,10573,10633,10700,10761,10826,10885,10950,11019,11082,11149,11203,11260,11331,11402", "endLines": "10,14,18,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138", "endColumns": "17,12,12,83,82,80,92,91,62,61,88,90,70,69,60,65,138,141,136,70,78,69,64,89,88,66,67,52,57,46,60,59,66,60,64,58,64,68,62,66,53,56,70,70,51", "endOffsets": "332,533,711,7755,7838,7919,8012,8104,8167,8229,8318,8409,8480,8550,8611,8677,8816,8958,9095,9166,9245,9315,9380,9470,9559,9626,9694,10402,10460,10507,10568,10628,10695,10756,10821,10880,10945,11014,11077,11144,11198,11255,11326,11397,11449"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\51b16512a656ca1125c5acd36144cb6d\\transformed\\exoplayer-core-2.18.1\\res\\values-si\\values-si.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,131,191,254,320,396,465,554,640", "endColumns": "75,59,62,65,75,68,88,85,69", "endOffsets": "126,186,249,315,391,460,549,635,705"}, "to": {"startLines": "112,113,114,115,116,117,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "9699,9775,9835,9898,9964,10040,10109,10198,10284", "endColumns": "75,59,62,65,75,68,88,85,69", "endOffsets": "9770,9830,9893,9959,10035,10104,10193,10279,10349"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\d139f7b0159c383eb11b4ff91a7586c3\\transformed\\material-1.6.1\\res\\values-si\\values-si.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,224,302,404,519,602,666,755,822,882,976,1039,1095,1165,1232,1287,1406,1463,1527,1581,1654,1776,1859,1944,2046,2124,2204,2290,2357,2423,2493,2566,2648,2720,2797,2869,2939,3032,3105,3195,3288,3362,3434,3525,3579,3645,3729,3814,3876,3940,4003,4108,4208,4303,4403", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,77,101,114,82,63,88,66,59,93,62,55,69,66,54,118,56,63,53,72,121,82,84,101,77,79,85,66,65,69,72,81,71,76,71,69,92,72,89,92,73,71,90,53,65,83,84,61,63,62,104,99,94,99,79", "endOffsets": "219,297,399,514,597,661,750,817,877,971,1034,1090,1160,1227,1282,1401,1458,1522,1576,1649,1771,1854,1939,2041,2119,2199,2285,2352,2418,2488,2561,2643,2715,2792,2864,2934,3027,3100,3190,3283,3357,3429,3520,3574,3640,3724,3809,3871,3935,3998,4103,4203,4298,4398,4478"}, "to": {"startLines": "19,51,59,60,61,87,139,144,149,151,152,153,154,155,156,157,158,159,160,161,162,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,214", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "716,3670,4480,4582,4697,7612,11454,11944,12338,12467,12561,12624,12680,12750,12817,12872,12991,13048,13112,13166,13239,13584,13667,13752,13854,13932,14012,14098,14165,14231,14301,14374,14456,14528,14605,14677,14747,14840,14913,15003,15096,15170,15242,15333,15387,15453,15537,15622,15684,15748,15811,15916,16016,16111,17426", "endLines": "22,51,59,60,61,87,139,144,149,151,152,153,154,155,156,157,158,159,160,161,162,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,214", "endColumns": "12,77,101,114,82,63,88,66,59,93,62,55,69,66,54,118,56,63,53,72,121,82,84,101,77,79,85,66,65,69,72,81,71,76,71,69,92,72,89,92,73,71,90,53,65,83,84,61,63,62,104,99,94,99,79", "endOffsets": "880,3743,4577,4692,4775,7671,11538,12006,12393,12556,12619,12675,12745,12812,12867,12986,13043,13107,13161,13234,13356,13662,13747,13849,13927,14007,14093,14160,14226,14296,14369,14451,14523,14600,14672,14742,14835,14908,14998,15091,15165,15237,15328,15382,15448,15532,15617,15679,15743,15806,15911,16011,16106,16206,17501"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\263c8e8847d6991526302086ecc6e182\\transformed\\play-services-base-18.0.1\\res\\values-si\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,297,447,567,676,814,934,1046,1140,1287,1398,1550,1677,1817,1974,2043,2100", "endColumns": "103,149,119,108,137,119,111,93,146,110,151,126,139,156,68,56,75", "endOffsets": "296,446,566,675,813,933,1045,1139,1286,1397,1549,1676,1816,1973,2042,2099,2175"}, "to": {"startLines": "65,66,67,68,69,70,71,72,74,75,76,77,78,79,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5034,5142,5296,5420,5533,5675,5799,5915,6152,6303,6418,6574,6705,6849,7010,7083,7144", "endColumns": "107,153,123,112,141,123,115,97,150,114,155,130,143,160,72,60,79", "endOffsets": "5137,5291,5415,5528,5670,5794,5910,6008,6298,6413,6569,6700,6844,7005,7078,7139,7219"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\37f986109817451798d14c7952a3b6e1\\transformed\\react-android-0.76.9-debug\\res\\values-si\\values-si.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,206,279,347,430,499,567,643,722,805,891,960,1040,1129,1209,1292,1377,1456,1533,1613,1705,1778,1857,1929", "endColumns": "68,81,72,67,82,68,67,75,78,82,85,68,79,88,79,82,84,78,76,79,91,72,78,71,77", "endOffsets": "119,201,274,342,425,494,562,638,717,800,886,955,1035,1124,1204,1287,1372,1451,1528,1608,1700,1773,1852,1924,2002"}, "to": {"startLines": "50,64,143,145,146,150,163,164,165,215,216,219,227,230,231,232,234,235,237,239,240,242,245,247,248", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3601,4952,11871,12011,12079,12398,13361,13429,13505,17506,17589,17836,18322,18557,18646,18726,18884,18969,19132,19290,19370,19563,19784,19981,20053", "endColumns": "68,81,72,67,82,68,67,75,78,82,85,68,79,88,79,82,84,78,76,79,91,72,78,71,77", "endOffsets": "3665,5029,11939,12074,12157,12462,13424,13500,13579,17584,17670,17900,18397,18641,18721,18804,18964,19043,19204,19365,19457,19631,19858,20048,20126"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\491ff7541ddc657f2c55a259a03f9653\\transformed\\ui-release\\res\\values-si\\values-si.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,194,277,376,475,557,642,733,819,899,978,1060,1133,1208,1292,1373,1454,1521", "endColumns": "88,82,98,98,81,84,90,85,79,78,81,72,74,83,80,80,66,117", "endOffsets": "189,272,371,470,552,637,728,814,894,973,1055,1128,1203,1287,1368,1449,1516,1634"}, "to": {"startLines": "62,63,84,85,86,147,148,202,203,217,218,229,233,236,238,243,244,246", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4780,4869,7332,7431,7530,12162,12247,16372,16458,17675,17754,18484,18809,19048,19209,19636,19717,19863", "endColumns": "88,82,98,98,81,84,90,85,79,78,81,72,74,83,80,80,66,117", "endOffsets": "4864,4947,7426,7525,7607,12242,12333,16453,16533,17749,17831,18552,18879,19127,19285,19712,19779,19976"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\268d4161c8b8edb1216e3f661bb7ee02\\transformed\\browser-1.6.0\\res\\values-si\\values-si.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,163,270,386", "endColumns": "107,106,115,104", "endOffsets": "158,265,381,486"}, "to": {"startLines": "83,140,141,142", "startColumns": "4,4,4,4", "startOffsets": "7224,11543,11650,11766", "endColumns": "107,106,115,104", "endOffsets": "7327,11645,11761,11866"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\02b86cfeeba2fc323c383047854b9491\\transformed\\play-services-basement-18.2.0\\res\\values-si\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "134", "endOffsets": "329"}, "to": {"startLines": "73", "startColumns": "4", "startOffsets": "6013", "endColumns": "138", "endOffsets": "6147"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\3767e343142fab2c839a998d5bfdc1ca\\transformed\\play-services-ads-22.6.0\\res\\values-si\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "199,245,294,348,414,482,578,666,763,814,928,988,1104,1196,1237,1325,1361,1406,1458,1537,1585", "endColumns": "45,48,53,65,67,95,87,96,50,113,59,115,91,40,87,35,44,51,78,47,55", "endOffsets": "244,293,347,413,481,577,665,762,813,927,987,1103,1195,1236,1324,1360,1405,1457,1536,1584,1640"}, "to": {"startLines": "199,200,201,204,205,206,207,208,209,210,211,212,213,220,221,222,223,224,225,226,251", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "16211,16261,16314,16538,16608,16680,16780,16872,16973,17028,17146,17210,17330,17905,17950,18042,18082,18131,18187,18270,20314", "endColumns": "49,52,57,69,71,99,91,100,54,117,63,119,95,44,91,39,48,55,82,51,59", "endOffsets": "16256,16309,16367,16603,16675,16775,16867,16968,17023,17141,17205,17325,17421,17945,18037,18077,18126,18182,18265,18317,20369"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\71743d480ebb399fb4fe6b96265e7bca\\transformed\\core-1.13.1\\res\\values-si\\values-si.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,260,365,470,569,673,787", "endColumns": "101,102,104,104,98,103,113,100", "endOffsets": "152,255,360,465,564,668,782,883"}, "to": {"startLines": "52,53,54,55,56,57,58,241", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3748,3850,3953,4058,4163,4262,4366,19462", "endColumns": "101,102,104,104,98,103,113,100", "endOffsets": "3845,3948,4053,4158,4257,4361,4475,19558"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\68cda876f16b6c181c93767ccf9edd36\\transformed\\appcompat-1.7.0\\res\\values-si\\values-si.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,221,328,435,518,623,739,829,915,1006,1099,1193,1287,1387,1480,1575,1669,1760,1851,1935,2044,2148,2246,2356,2456,2563,2722,2821", "endColumns": "115,106,106,82,104,115,89,85,90,92,93,93,99,92,94,93,90,90,83,108,103,97,109,99,106,158,98,81", "endOffsets": "216,323,430,513,618,734,824,910,1001,1094,1188,1282,1382,1475,1570,1664,1755,1846,1930,2039,2143,2241,2351,2451,2558,2717,2816,2898"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,228", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "885,1001,1108,1215,1298,1403,1519,1609,1695,1786,1879,1973,2067,2167,2260,2355,2449,2540,2631,2715,2824,2928,3026,3136,3236,3343,3502,18402", "endColumns": "115,106,106,82,104,115,89,85,90,92,93,93,99,92,94,93,90,90,83,108,103,97,109,99,106,158,98,81", "endOffsets": "996,1103,1210,1293,1398,1514,1604,1690,1781,1874,1968,2062,2162,2255,2350,2444,2535,2626,2710,2819,2923,3021,3131,3231,3338,3497,3596,18479"}}]}]}