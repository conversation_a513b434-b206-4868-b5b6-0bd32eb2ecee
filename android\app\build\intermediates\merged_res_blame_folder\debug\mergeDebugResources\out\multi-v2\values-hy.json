{"logs": [{"outputFile": "com.gokul719.snack97152fc1f368437dac54171df4ba22bd.app-mergeDebugResources-74:/values-hy/values-hy.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\02b86cfeeba2fc323c383047854b9491\\transformed\\play-services-basement-18.2.0\\res\\values-hy\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "146", "endOffsets": "341"}, "to": {"startLines": "72", "startColumns": "4", "startOffsets": "5990", "endColumns": "150", "endOffsets": "6136"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f1013ccf4ee59567949af9e71a98b092\\transformed\\exoplayer-ui-2.18.1\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,288,477,659,742,824,894,985,1081,1157,1220,1321,1424,1494,1562,1630,1696,1818,1934,2054,2118,2199,2276,2354,2450,2545,2614,2679,2732,2792,2840,2901,2969,3037,3110,3177,3238,3299,3366,3431,3501,3553,3615,3691,3767", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,82,81,69,90,95,75,62,100,102,69,67,67,65,121,115,119,63,80,76,77,95,94,68,64,52,59,47,60,67,67,72,66,60,60,66,64,69,51,61,75,75,52", "endOffsets": "283,472,654,737,819,889,980,1076,1152,1215,1316,1419,1489,1557,1625,1691,1813,1929,2049,2113,2194,2271,2349,2445,2540,2609,2674,2727,2787,2835,2896,2964,3032,3105,3172,3233,3294,3361,3426,3496,3548,3610,3686,3762,3815"}, "to": {"startLines": "2,11,15,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,338,527,7707,7790,7872,7942,8033,8129,8205,8268,8369,8472,8542,8610,8678,8744,8866,8982,9102,9166,9247,9324,9402,9498,9593,9662,10381,10434,10494,10542,10603,10671,10739,10812,10879,10940,11001,11068,11133,11203,11255,11317,11393,11469", "endLines": "10,14,18,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137", "endColumns": "17,12,12,82,81,69,90,95,75,62,100,102,69,67,67,65,121,115,119,63,80,76,77,95,94,68,64,52,59,47,60,67,67,72,66,60,60,66,64,69,51,61,75,75,52", "endOffsets": "333,522,704,7785,7867,7937,8028,8124,8200,8263,8364,8467,8537,8605,8673,8739,8861,8977,9097,9161,9242,9319,9397,9493,9588,9657,9722,10429,10489,10537,10598,10666,10734,10807,10874,10935,10996,11063,11128,11198,11250,11312,11388,11464,11517"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\71743d480ebb399fb4fe6b96265e7bca\\transformed\\core-1.13.1\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,260,358,457,562,664,775", "endColumns": "99,104,97,98,104,101,110,100", "endOffsets": "150,255,353,452,557,659,770,871"}, "to": {"startLines": "51,52,53,54,55,56,57,227", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3663,3763,3868,3966,4065,4170,4272,18597", "endColumns": "99,104,97,98,104,101,110,100", "endOffsets": "3758,3863,3961,4060,4165,4267,4378,18693"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\51b16512a656ca1125c5acd36144cb6d\\transformed\\exoplayer-core-2.18.1\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,127,190,254,322,403,480,554,631", "endColumns": "71,62,63,67,80,76,73,76,77", "endOffsets": "122,185,249,317,398,475,549,626,704"}, "to": {"startLines": "111,112,113,114,115,116,117,118,119", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "9727,9799,9862,9926,9994,10075,10152,10226,10303", "endColumns": "71,62,63,67,80,76,73,76,77", "endOffsets": "9794,9857,9921,9989,10070,10147,10221,10298,10376"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\491ff7541ddc657f2c55a259a03f9653\\transformed\\ui-release\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,204,286,380,480,563,645,731,826,908,993,1081,1155,1232,1311,1388,1469,1538", "endColumns": "98,81,93,99,82,81,85,94,81,84,87,73,76,78,76,80,68,117", "endOffsets": "199,281,375,475,558,640,726,821,903,988,1076,1150,1227,1306,1383,1464,1533,1651"}, "to": {"startLines": "61,62,83,84,85,145,146,198,199,211,212,221,222,223,225,228,229,230", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4689,4788,7366,7460,7560,12170,12252,16311,16406,17489,17574,18141,18215,18292,18446,18698,18779,18848", "endColumns": "98,81,93,99,82,81,85,94,81,84,87,73,76,78,76,80,68,117", "endOffsets": "4783,4865,7455,7555,7638,12247,12333,16401,16483,17569,17657,18210,18287,18366,18518,18774,18843,18961"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\37f986109817451798d14c7952a3b6e1\\transformed\\react-android-0.76.9-debug\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,132,200,281,349,421,496", "endColumns": "76,67,80,67,71,74,73", "endOffsets": "127,195,276,344,416,491,565"}, "to": {"startLines": "63,143,144,148,161,224,226", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "4870,12021,12089,12400,13371,18371,18523", "endColumns": "76,67,80,67,71,74,73", "endOffsets": "4942,12084,12165,12463,13438,18441,18592"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c53b383fa8484255d5fd97643f23aa97\\transformed\\foundation-release\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,140", "endColumns": "84,88", "endOffsets": "135,224"}, "to": {"startLines": "231,232", "startColumns": "4,4", "startOffsets": "18966,19051", "endColumns": "84,88", "endOffsets": "19046,19135"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\3767e343142fab2c839a998d5bfdc1ca\\transformed\\play-services-ads-22.6.0\\res\\values-hy\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "199,250,299,352,420,489,597,684,788,840,968,1036,1134,1228,1269,1347,1383,1417,1473,1551,1596", "endColumns": "50,48,52,67,68,107,86,103,51,127,67,97,93,40,77,35,33,55,77,44,55", "endOffsets": "249,298,351,419,488,596,683,787,839,967,1035,1133,1227,1268,1346,1382,1416,1472,1550,1595,1651"}, "to": {"startLines": "195,196,197,200,201,202,203,204,205,206,207,208,209,213,214,215,216,217,218,219,233", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "16146,16201,16254,16488,16560,16633,16745,16836,16944,17000,17132,17204,17306,17662,17707,17789,17829,17867,17927,18009,19140", "endColumns": "54,52,56,71,72,111,90,107,55,131,71,101,97,44,81,39,37,59,81,48,59", "endOffsets": "16196,16249,16306,16555,16628,16740,16831,16939,16995,17127,17199,17301,17399,17702,17784,17824,17862,17922,18004,18053,19195"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\68cda876f16b6c181c93767ccf9edd36\\transformed\\appcompat-1.7.0\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,313,423,512,618,735,817,897,988,1081,1176,1270,1370,1463,1558,1652,1743,1834,1917,2023,2129,2228,2338,2446,2547,2717,2814", "endColumns": "107,99,109,88,105,116,81,79,90,92,94,93,99,92,94,93,90,90,82,105,105,98,109,107,100,169,96,82", "endOffsets": "208,308,418,507,613,730,812,892,983,1076,1171,1265,1365,1458,1553,1647,1738,1829,1912,2018,2124,2223,2333,2441,2542,2712,2809,2892"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,220", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "874,982,1082,1192,1281,1387,1504,1586,1666,1757,1850,1945,2039,2139,2232,2327,2421,2512,2603,2686,2792,2898,2997,3107,3215,3316,3486,18058", "endColumns": "107,99,109,88,105,116,81,79,90,92,94,93,99,92,94,93,90,90,82,105,105,98,109,107,100,169,96,82", "endOffsets": "977,1077,1187,1276,1382,1499,1581,1661,1752,1845,1940,2034,2134,2227,2322,2416,2507,2598,2681,2787,2893,2992,3102,3210,3311,3481,3578,18136"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\268d4161c8b8edb1216e3f661bb7ee02\\transformed\\browser-1.6.0\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,159,262,373", "endColumns": "103,102,110,102", "endOffsets": "154,257,368,471"}, "to": {"startLines": "82,139,140,141", "startColumns": "4,4,4,4", "startOffsets": "7262,11619,11722,11833", "endColumns": "103,102,110,102", "endOffsets": "7361,11717,11828,11931"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\d139f7b0159c383eb11b4ff91a7586c3\\transformed\\material-1.6.1\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,220,300,395,525,606,670,767,852,914,1001,1065,1126,1193,1254,1308,1430,1487,1547,1601,1682,1817,1901,1986,2092,2167,2242,2337,2404,2470,2544,2624,2710,2781,2857,2933,3010,3098,3178,3274,3370,3444,3522,3622,3673,3742,3829,3920,3982,4046,4109,4214,4315,4415,4520", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,79,94,129,80,63,96,84,61,86,63,60,66,60,53,121,56,59,53,80,134,83,84,105,74,74,94,66,65,73,79,85,70,75,75,76,87,79,95,95,73,77,99,50,68,86,90,61,63,62,104,100,99,104,84", "endOffsets": "215,295,390,520,601,665,762,847,909,996,1060,1121,1188,1249,1303,1425,1482,1542,1596,1677,1812,1896,1981,2087,2162,2237,2332,2399,2465,2539,2619,2705,2776,2852,2928,3005,3093,3173,3269,3365,3439,3517,3617,3668,3737,3824,3915,3977,4041,4104,4209,4310,4410,4515,4600"}, "to": {"startLines": "19,50,58,59,60,86,138,142,147,149,150,151,152,153,154,155,156,157,158,159,160,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,210", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "709,3583,4383,4478,4608,7643,11522,11936,12338,12468,12555,12619,12680,12747,12808,12862,12984,13041,13101,13155,13236,13443,13527,13612,13718,13793,13868,13963,14030,14096,14170,14250,14336,14407,14483,14559,14636,14724,14804,14900,14996,15070,15148,15248,15299,15368,15455,15546,15608,15672,15735,15840,15941,16041,17404", "endLines": "22,50,58,59,60,86,138,142,147,149,150,151,152,153,154,155,156,157,158,159,160,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,210", "endColumns": "12,79,94,129,80,63,96,84,61,86,63,60,66,60,53,121,56,59,53,80,134,83,84,105,74,74,94,66,65,73,79,85,70,75,75,76,87,79,95,95,73,77,99,50,68,86,90,61,63,62,104,100,99,104,84", "endOffsets": "869,3658,4473,4603,4684,7702,11614,12016,12395,12550,12614,12675,12742,12803,12857,12979,13036,13096,13150,13231,13366,13522,13607,13713,13788,13863,13958,14025,14091,14165,14245,14331,14402,14478,14554,14631,14719,14799,14895,14991,15065,15143,15243,15294,15363,15450,15541,15603,15667,15730,15835,15936,16036,16141,17484"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\263c8e8847d6991526302086ecc6e182\\transformed\\play-services-base-18.0.1\\res\\values-hy\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,456,586,690,840,972,1095,1204,1367,1471,1635,1767,1925,2087,2148,2211", "endColumns": "101,160,129,103,149,131,122,108,162,103,163,131,157,161,60,62,77", "endOffsets": "294,455,585,689,839,971,1094,1203,1366,1470,1634,1766,1924,2086,2147,2210,2288"}, "to": {"startLines": "64,65,66,67,68,69,70,71,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4947,5053,5218,5352,5460,5614,5750,5877,6141,6308,6416,6584,6720,6882,7048,7113,7180", "endColumns": "105,164,133,107,153,135,126,112,166,107,167,135,161,165,64,66,81", "endOffsets": "5048,5213,5347,5455,5609,5745,5872,5985,6303,6411,6579,6715,6877,7043,7108,7175,7257"}}]}]}