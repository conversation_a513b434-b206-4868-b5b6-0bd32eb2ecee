{"logs": [{"outputFile": "com.gokul719.snack97152fc1f368437dac54171df4ba22bd.app-mergeDebugResources-74:/values-or/values-or.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\263c8e8847d6991526302086ecc6e182\\transformed\\play-services-base-18.0.1\\res\\values-or\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,300,457,585,698,849,980,1090,1196,1359,1468,1625,1754,1900,2053,2114,2182", "endColumns": "106,156,127,112,150,130,109,105,162,108,156,128,145,152,60,67,82", "endOffsets": "299,456,584,697,848,979,1089,1195,1358,1467,1624,1753,1899,2052,2113,2181,2264"}, "to": {"startLines": "46,47,48,49,50,51,52,53,55,56,57,58,59,60,61,62,63", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4303,4414,4575,4707,4824,4979,5114,5228,5478,5645,5758,5919,6052,6202,6359,6424,6496", "endColumns": "110,160,131,116,154,134,113,109,166,112,160,132,149,156,64,71,86", "endOffsets": "4409,4570,4702,4819,4974,5109,5223,5333,5640,5753,5914,6047,6197,6354,6419,6491,6578"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c53b383fa8484255d5fd97643f23aa97\\transformed\\foundation-release\\res\\values-or\\values-or.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,140", "endColumns": "84,87", "endOffsets": "135,223"}, "to": {"startLines": "156,157", "startColumns": "4,4", "startOffsets": "13933,14018", "endColumns": "84,87", "endOffsets": "14013,14101"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\491ff7541ddc657f2c55a259a03f9653\\transformed\\ui-release\\res\\values-or\\values-or.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,289,381,481,567,644,742,830,917,995,1077,1147,1222,1299,1375,1458,1525", "endColumns": "96,86,91,99,85,76,97,87,86,77,81,69,74,76,75,82,66,118", "endOffsets": "197,284,376,476,562,639,737,825,912,990,1072,1142,1217,1294,1370,1453,1520,1639"}, "to": {"startLines": "44,45,65,66,67,74,75,125,126,138,139,148,149,150,151,153,154,155", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4119,4216,6693,6785,6885,7517,7594,11403,11491,12550,12628,13265,13335,13410,13487,13664,13747,13814", "endColumns": "96,86,91,99,85,76,97,87,86,77,81,69,74,76,75,82,66,118", "endOffsets": "4211,4298,6780,6880,6966,7589,7687,11486,11573,12623,12705,13330,13405,13482,13558,13742,13809,13928"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\02b86cfeeba2fc323c383047854b9491\\transformed\\play-services-basement-18.2.0\\res\\values-or\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "135", "endOffsets": "330"}, "to": {"startLines": "54", "startColumns": "4", "startOffsets": "5338", "endColumns": "139", "endOffsets": "5473"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\d139f7b0159c383eb11b4ff91a7586c3\\transformed\\material-1.6.1\\res\\values-or\\values-or.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,227,304,398,503,582,647,736,801,860,946,1010,1073,1146,1210,1264,1376,1434,1496,1550,1622,1744,1831,1917,2027,2104,2185,2276,2343,2409,2479,2556,2643,2714,2791,2860,2929,3020,3092,3181,3270,3344,3416,3502,3552,3618,3698,3782,3844,3908,3971,4071,4168,4260,4359", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,76,93,104,78,64,88,64,58,85,63,62,72,63,53,111,57,61,53,71,121,86,85,109,76,80,90,66,65,69,76,86,70,76,68,68,90,71,88,88,73,71,85,49,65,79,83,61,63,62,99,96,91,98,83", "endOffsets": "222,299,393,498,577,642,731,796,855,941,1005,1068,1141,1205,1259,1371,1429,1491,1545,1617,1739,1826,1912,2022,2099,2180,2271,2338,2404,2474,2551,2638,2709,2786,2855,2924,3015,3087,3176,3265,3339,3411,3497,3547,3613,3693,3777,3839,3903,3966,4066,4163,4255,4354,4438"}, "to": {"startLines": "2,33,41,42,43,68,69,73,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,137", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3029,3841,3935,4040,6971,7036,7452,7692,7751,7837,7901,7964,8037,8101,8155,8267,8325,8387,8441,8513,8635,8722,8808,8918,8995,9076,9167,9234,9300,9370,9447,9534,9605,9682,9751,9820,9911,9983,10072,10161,10235,10307,10393,10443,10509,10589,10673,10735,10799,10862,10962,11059,11151,12466", "endLines": "5,33,41,42,43,68,69,73,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,137", "endColumns": "12,76,93,104,78,64,88,64,58,85,63,62,72,63,53,111,57,61,53,71,121,86,85,109,76,80,90,66,65,69,76,86,70,76,68,68,90,71,88,88,73,71,85,49,65,79,83,61,63,62,99,96,91,98,83", "endOffsets": "272,3101,3930,4035,4114,7031,7120,7512,7746,7832,7896,7959,8032,8096,8150,8262,8320,8382,8436,8508,8630,8717,8803,8913,8990,9071,9162,9229,9295,9365,9442,9529,9600,9677,9746,9815,9906,9978,10067,10156,10230,10302,10388,10438,10504,10584,10668,10730,10794,10857,10957,11054,11146,11245,12545"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\71743d480ebb399fb4fe6b96265e7bca\\transformed\\core-1.13.1\\res\\values-or\\values-or.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,158,260,363,468,569,671,790", "endColumns": "102,101,102,104,100,101,118,100", "endOffsets": "153,255,358,463,564,666,785,886"}, "to": {"startLines": "34,35,36,37,38,39,40,152", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3106,3209,3311,3414,3519,3620,3722,13563", "endColumns": "102,101,102,104,100,101,118,100", "endOffsets": "3204,3306,3409,3514,3615,3717,3836,13659"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\68cda876f16b6c181c93767ccf9edd36\\transformed\\appcompat-1.7.0\\res\\values-or\\values-or.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,326,433,519,623,743,822,903,994,1087,1188,1283,1383,1476,1571,1667,1758,1848,1937,2047,2151,2257,2368,2470,2588,2751,2857", "endColumns": "110,109,106,85,103,119,78,80,90,92,100,94,99,92,94,95,90,89,88,109,103,105,110,101,117,162,105,89", "endOffsets": "211,321,428,514,618,738,817,898,989,1082,1183,1278,1378,1471,1566,1662,1753,1843,1932,2042,2146,2252,2363,2465,2583,2746,2852,2942"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,147", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "277,388,498,605,691,795,915,994,1075,1166,1259,1360,1455,1555,1648,1743,1839,1930,2020,2109,2219,2323,2429,2540,2642,2760,2923,13175", "endColumns": "110,109,106,85,103,119,78,80,90,92,100,94,99,92,94,95,90,89,88,109,103,105,110,101,117,162,105,89", "endOffsets": "383,493,600,686,790,910,989,1070,1161,1254,1355,1450,1550,1643,1738,1834,1925,2015,2104,2214,2318,2424,2535,2637,2755,2918,3024,13260"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\268d4161c8b8edb1216e3f661bb7ee02\\transformed\\browser-1.6.0\\res\\values-or\\values-or.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,165,270,383", "endColumns": "109,104,112,108", "endOffsets": "160,265,378,487"}, "to": {"startLines": "64,70,71,72", "startColumns": "4,4,4,4", "startOffsets": "6583,7125,7230,7343", "endColumns": "109,104,112,108", "endOffsets": "6688,7225,7338,7447"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\3767e343142fab2c839a998d5bfdc1ca\\transformed\\play-services-ads-22.6.0\\res\\values-or\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "199,239,287,340,412,490,590,670,774,829,932,988,1103,1188,1234,1337,1377,1424,1484,1577,1625", "endColumns": "39,47,52,71,77,99,79,103,54,102,55,114,84,45,102,39,46,59,92,47,53", "endOffsets": "238,286,339,411,489,589,669,773,828,931,987,1102,1187,1233,1336,1376,1423,1483,1576,1624,1678"}, "to": {"startLines": "122,123,124,127,128,129,130,131,132,133,134,135,136,140,141,142,143,144,145,146,158", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "11250,11294,11346,11578,11654,11736,11840,11924,12032,12091,12198,12258,12377,12710,12760,12867,12911,12962,13026,13123,14106", "endColumns": "43,51,56,75,81,103,83,107,58,106,59,118,88,49,106,43,50,63,96,51,57", "endOffsets": "11289,11341,11398,11649,11731,11835,11919,12027,12086,12193,12253,12372,12461,12755,12862,12906,12957,13021,13118,13170,14159"}}]}]}