{"logs": [{"outputFile": "com.gokul719.snack97152fc1f368437dac54171df4ba22bd.app-mergeDebugResources-74:/values-cs/values-cs.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\37f986109817451798d14c7952a3b6e1\\transformed\\react-android-0.76.9-debug\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,127,210,281,352,439,507,577,656,738,824,910,980,1056,1133,1215,1296,1378,1453,1524,1594,1678,1751,1829,1900", "endColumns": "71,82,70,70,86,67,69,78,81,85,85,69,75,76,81,80,81,74,70,69,83,72,77,70,79", "endOffsets": "122,205,276,347,434,502,572,651,733,819,905,975,1051,1128,1210,1291,1373,1448,1519,1589,1673,1746,1824,1895,1975"}, "to": {"startLines": "56,70,152,159,160,164,177,178,179,230,231,234,242,245,246,247,249,250,252,254,255,257,260,262,263", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3936,5291,12515,12988,13059,13377,14324,14394,14473,18571,18657,18911,19384,19615,19692,19774,19933,20015,20166,20312,20382,20567,20787,20985,21056", "endColumns": "71,82,70,70,86,67,69,78,81,85,85,69,75,76,81,80,81,74,70,69,83,72,77,70,79", "endOffsets": "4003,5369,12581,13054,13141,13440,14389,14468,14550,18652,18738,18976,19455,19687,19769,19850,20010,20085,20232,20377,20461,20635,20860,21051,21131"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\084b6a12791f1cb8a95d16811ba661d8\\transformed\\Android-Image-Cropper-4.3.1\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,169,216,274,323,395,462,546,600", "endColumns": "113,46,57,48,71,66,83,53,65", "endOffsets": "164,211,269,318,390,457,541,595,661"}, "to": {"startLines": "90,91,92,153,154,155,156,157,229", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "7728,7842,7889,12586,12635,12707,12774,12858,18505", "endColumns": "113,46,57,48,71,66,83,53,65", "endOffsets": "7837,7884,7942,12630,12702,12769,12853,12907,18566"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\d139f7b0159c383eb11b4ff91a7586c3\\transformed\\material-1.6.1\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,323,400,492,618,699,764,863,939,1000,1089,1156,1210,1278,1338,1392,1509,1569,1631,1685,1757,1879,1963,2055,2162,2240,2322,2410,2477,2543,2615,2692,2776,2848,2925,2999,3070,3158,3229,3322,3417,3491,3565,3661,3713,3780,3866,3954,4016,4080,4143,4253,4349,4448,4546", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "endColumns": "12,76,91,125,80,64,98,75,60,88,66,53,67,59,53,116,59,61,53,71,121,83,91,106,77,81,87,66,65,71,76,83,71,76,73,70,87,70,92,94,73,73,95,51,66,85,87,61,63,62,109,95,98,97,78", "endOffsets": "318,395,487,613,694,759,858,934,995,1084,1151,1205,1273,1333,1387,1504,1564,1626,1680,1752,1874,1958,2050,2157,2235,2317,2405,2472,2538,2610,2687,2771,2843,2920,2994,3065,3153,3224,3317,3412,3486,3560,3656,3708,3775,3861,3949,4011,4075,4138,4248,4344,4443,4541,4620"}, "to": {"startLines": "23,57,65,66,67,96,148,158,163,165,166,167,168,169,170,171,172,173,174,175,176,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,228", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "974,4008,4816,4908,5034,8235,12098,12912,13316,13445,13534,13601,13655,13723,13783,13837,13954,14014,14076,14130,14202,14555,14639,14731,14838,14916,14998,15086,15153,15219,15291,15368,15452,15524,15601,15675,15746,15834,15905,15998,16093,16167,16241,16337,16389,16456,16542,16630,16692,16756,16819,16929,17025,17124,18426", "endLines": "28,57,65,66,67,96,148,158,163,165,166,167,168,169,170,171,172,173,174,175,176,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,228", "endColumns": "12,76,91,125,80,64,98,75,60,88,66,53,67,59,53,116,59,61,53,71,121,83,91,106,77,81,87,66,65,71,76,83,71,76,73,70,87,70,92,94,73,73,95,51,66,85,87,61,63,62,109,95,98,97,78", "endOffsets": "1237,4080,4903,5029,5110,8295,12192,12983,13372,13529,13596,13650,13718,13778,13832,13949,14009,14071,14125,14197,14319,14634,14726,14833,14911,14993,15081,15148,15214,15286,15363,15447,15519,15596,15670,15741,15829,15900,15993,16088,16162,16236,16332,16384,16451,16537,16625,16687,16751,16814,16924,17020,17119,17217,18500"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\268d4161c8b8edb1216e3f661bb7ee02\\transformed\\browser-1.6.0\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,157,260,374", "endColumns": "101,102,113,100", "endOffsets": "152,255,369,470"}, "to": {"startLines": "89,149,150,151", "startColumns": "4,4,4,4", "startOffsets": "7626,12197,12300,12414", "endColumns": "101,102,113,100", "endOffsets": "7723,12295,12409,12510"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\71743d480ebb399fb4fe6b96265e7bca\\transformed\\core-1.13.1\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,356,455,560,667,786", "endColumns": "97,101,100,98,104,106,118,100", "endOffsets": "148,250,351,450,555,662,781,882"}, "to": {"startLines": "58,59,60,61,62,63,64,256", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4085,4183,4285,4386,4485,4590,4697,20466", "endColumns": "97,101,100,98,104,106,118,100", "endOffsets": "4178,4280,4381,4480,4585,4692,4811,20562"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\02b86cfeeba2fc323c383047854b9491\\transformed\\play-services-basement-18.2.0\\res\\values-cs\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "138", "endOffsets": "333"}, "to": {"startLines": "79", "startColumns": "4", "startOffsets": "6368", "endColumns": "142", "endOffsets": "6506"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\491ff7541ddc657f2c55a259a03f9653\\transformed\\ui-release\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,281,375,477,569,647,739,830,911,993,1079,1151,1229,1305,1380,1459,1527", "endColumns": "92,82,93,101,91,77,91,90,80,81,85,71,77,75,74,78,67,119", "endOffsets": "193,276,370,472,564,642,734,825,906,988,1074,1146,1224,1300,1375,1454,1522,1642"}, "to": {"startLines": "68,69,93,94,95,161,162,216,217,232,233,244,248,251,253,258,259,261", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5115,5208,7947,8041,8143,13146,13224,17393,17484,18743,18825,19543,19855,20090,20237,20640,20719,20865", "endColumns": "92,82,93,101,91,77,91,90,80,81,85,71,77,75,74,78,67,119", "endOffsets": "5203,5286,8036,8138,8230,13219,13311,17479,17560,18820,18906,19610,19928,20161,20307,20714,20782,20980"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\3767e343142fab2c839a998d5bfdc1ca\\transformed\\play-services-ads-22.6.0\\res\\values-cs\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "199,240,287,358,423,492,591,668,784,834,939,995,1091,1179,1222,1304,1340,1377,1433,1510,1554", "endColumns": "40,46,70,64,68,98,76,115,49,104,55,95,87,42,81,35,36,55,76,43,55", "endOffsets": "239,286,357,422,491,590,667,783,833,938,994,1090,1178,1221,1303,1339,1376,1432,1509,1553,1609"}, "to": {"startLines": "213,214,215,218,219,220,221,222,223,224,225,226,227,235,236,237,238,239,240,241,266", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "17222,17267,17318,17565,17634,17707,17810,17891,18011,18065,18174,18234,18334,18981,19028,19114,19154,19195,19255,19336,21311", "endColumns": "44,50,74,68,72,102,80,119,53,108,59,99,91,46,85,39,40,59,80,47,59", "endOffsets": "17262,17313,17388,17629,17702,17805,17886,18006,18060,18169,18229,18329,18421,19023,19109,19149,19190,19250,19331,19379,21366"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\263c8e8847d6991526302086ecc6e182\\transformed\\play-services-base-18.0.1\\res\\values-cs\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,451,572,678,827,950,1058,1155,1326,1433,1593,1717,1874,2025,2089,2152", "endColumns": "101,155,120,105,148,122,107,96,170,106,159,123,156,150,63,62,81", "endOffsets": "294,450,571,677,826,949,1057,1154,1325,1432,1592,1716,1873,2024,2088,2151,2233"}, "to": {"startLines": "71,72,73,74,75,76,77,78,80,81,82,83,84,85,86,87,88", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5374,5480,5640,5765,5875,6028,6155,6267,6511,6686,6797,6961,7089,7250,7405,7473,7540", "endColumns": "105,159,124,109,152,126,111,100,174,110,163,127,160,154,67,66,85", "endOffsets": "5475,5635,5760,5870,6023,6150,6262,6363,6681,6792,6956,7084,7245,7400,7468,7535,7621"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\51b16512a656ca1125c5acd36144cb6d\\transformed\\exoplayer-core-2.18.1\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,131,193,256,325,402,472,554,634", "endColumns": "75,61,62,68,76,69,81,79,79", "endOffsets": "126,188,251,320,397,467,549,629,709"}, "to": {"startLines": "121,122,123,124,125,126,127,128,129", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "10314,10390,10452,10515,10584,10661,10731,10813,10893", "endColumns": "75,61,62,68,76,69,81,79,79", "endOffsets": "10385,10447,10510,10579,10656,10726,10808,10888,10968"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f1013ccf4ee59567949af9e71a98b092\\transformed\\exoplayer-ui-2.18.1\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,11,17,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,287,610,924,1005,1085,1163,1265,1363,1441,1505,1594,1686,1756,1822,1887,1959,2072,2187,2310,2384,2464,2536,2617,2711,2806,2873,2938,2991,3049,3097,3158,3224,3291,3354,3421,3486,3545,3610,3674,3740,3792,3855,3932,4009", "endLines": "10,16,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64", "endColumns": "17,12,12,80,79,77,101,97,77,63,88,91,69,65,64,71,112,114,122,73,79,71,80,93,94,66,64,52,57,47,60,65,66,62,66,64,58,64,63,65,51,62,76,76,53", "endOffsets": "282,605,919,1000,1080,1158,1260,1358,1436,1500,1589,1681,1751,1817,1882,1954,2067,2182,2305,2379,2459,2531,2612,2706,2801,2868,2933,2986,3044,3092,3153,3219,3286,3349,3416,3481,3540,3605,3669,3735,3787,3850,3927,4004,4058"}, "to": {"startLines": "2,11,17,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,337,660,8300,8381,8461,8539,8641,8739,8817,8881,8970,9062,9132,9198,9263,9335,9448,9563,9686,9760,9840,9912,9993,10087,10182,10249,10973,11026,11084,11132,11193,11259,11326,11389,11456,11521,11580,11645,11709,11775,11827,11890,11967,12044", "endLines": "10,16,22,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147", "endColumns": "17,12,12,80,79,77,101,97,77,63,88,91,69,65,64,71,112,114,122,73,79,71,80,93,94,66,64,52,57,47,60,65,66,62,66,64,58,64,63,65,51,62,76,76,53", "endOffsets": "332,655,969,8376,8456,8534,8636,8734,8812,8876,8965,9057,9127,9193,9258,9330,9443,9558,9681,9755,9835,9907,9988,10082,10177,10244,10309,11021,11079,11127,11188,11254,11321,11384,11451,11516,11575,11640,11704,11770,11822,11885,11962,12039,12093"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\68cda876f16b6c181c93767ccf9edd36\\transformed\\appcompat-1.7.0\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,314,424,510,615,732,810,886,977,1070,1165,1259,1353,1446,1541,1638,1729,1820,1904,2008,2120,2219,2325,2436,2538,2701,2799", "endColumns": "106,101,109,85,104,116,77,75,90,92,94,93,93,92,94,96,90,90,83,103,111,98,105,110,101,162,97,82", "endOffsets": "207,309,419,505,610,727,805,881,972,1065,1160,1254,1348,1441,1536,1633,1724,1815,1899,2003,2115,2214,2320,2431,2533,2696,2794,2877"}, "to": {"startLines": "29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,243", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1242,1349,1451,1561,1647,1752,1869,1947,2023,2114,2207,2302,2396,2490,2583,2678,2775,2866,2957,3041,3145,3257,3356,3462,3573,3675,3838,19460", "endColumns": "106,101,109,85,104,116,77,75,90,92,94,93,93,92,94,96,90,90,83,103,111,98,105,110,101,162,97,82", "endOffsets": "1344,1446,1556,1642,1747,1864,1942,2018,2109,2202,2297,2391,2485,2578,2673,2770,2861,2952,3036,3140,3252,3351,3457,3568,3670,3833,3931,19538"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c53b383fa8484255d5fd97643f23aa97\\transformed\\foundation-release\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,141", "endColumns": "85,88", "endOffsets": "136,225"}, "to": {"startLines": "264,265", "startColumns": "4,4", "startOffsets": "21136,21222", "endColumns": "85,88", "endOffsets": "21217,21306"}}]}]}